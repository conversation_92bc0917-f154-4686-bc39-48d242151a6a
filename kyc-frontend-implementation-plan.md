# KYC Field Whitespace Validation - Frontend Implementation Plan

## Executive Summary

This plan implements frontend-only changes to address KYC field validation gaps identified in the analysis report. The implementation will enhance existing validation patterns to reject whitespace-only inputs while maintaining backward compatibility and following established codebase patterns.

## Analysis Summary

### Current State Issues
- **95% of KYC string fields** accept whitespace-only inputs ("   ", "\t\t", "\n\n")
- **Only 13 fields** have automatic trimming via `fieldsToRemoveWhitespace`
- **Only 1 validator** (`requiredNonEmptyString`) properly handles whitespace validation (used for PASSWORD only)
- **Fragmented validation** approaches across similar field types

### Acceptance Criteria Mapping
Based on `kyc-field-task.md`, the implementation must meet these criteria:

#### Functional Requirements
1. ✅ **Mandatory fields reject whitespace-only inputs** → Enhance existing validators
2. ✅ **Optional fields accept empty OR valid content** → Conditional validation logic
3. ✅ **Input trimming applied consistently** → Expand `fieldsToRemoveWhitespace` array
4. ✅ **Field-specific validation rules** → Create specialized validators for names, addresses, etc.
5. ✅ **Single-character inputs accepted** → Preserve legitimate single chars like "J"
6. ✅ **International characters supported** → Unicode regex for name fields
7. ✅ **Existing valid data unaffected** → Backward compatibility maintained

#### Technical Requirements
1. ✅ **Centralized validation utility** → Enhance existing `validators.ts`
2. ✅ **Consistent error codes** → Follow existing translation patterns
3. ✅ **Applied across all KYC forms** → Update `useKYCValidators.tsx`
4. ✅ **Minimal performance impact** → Lightweight regex validation

## Implementation Strategy

### Phase 1: Enhanced Validators Creation
**File**: `src/app/utilities/validators.ts`
**Approach**: Extend existing `requiredNonEmptyString` pattern with field-specific variants

### Phase 2: KYC Form Integration
**File**: `src/app/utilities/kycPresets/useKYCValidators.tsx`
**Approach**: Replace `requiredString` with enhanced validators by field category

### Phase 3: Trimming Logic Expansion
**File**: `src/app/utilities/kycPresets/index.tsx`
**Approach**: Expand `fieldsToRemoveWhitespace` array with all string fields

### Phase 4: Error Messages
**File**: `public/locales/en/common.json`
**Approach**: Add new error codes following existing patterns

## Detailed Implementation Plan

### 1. Enhanced Validators (src/app/utilities/validators.ts)

#### 1.1 Core Validation Utility
```typescript
// Enhanced version of existing requiredNonEmptyString with options
const requiredNonEmptyStringWithOptions = (
    field: PropertyPath, 
    options: {
        customValidator?: (value: string) => boolean;
        errorMessage?: string;
    } = {}
) => validators.custom(field, (value, values, errors, context) => {
    const input = trim(value);
    
    // Check for whitespace-only input or empty string
    if (whiteSpace.test(input) || empty.test(input)) {
        return context.defaultMessages.requiredValue;
    }
    
    // Apply field-specific validation if provided
    if (options.customValidator && !options.customValidator(input)) {
        return options.errorMessage || context.defaultMessages.invalidFormat || 'Invalid format';
    }
    
    return null;
});
```

#### 1.2 Field-Specific Validators
```typescript
// Name fields - Unicode support for international names
const validKYCName = (field: PropertyPath) =>
    requiredNonEmptyStringWithOptions(field, {
        customValidator: (value) => /^[\p{L}\p{M}\s\-'\.]+$/u.test(value),
        errorMessage: 'Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed',
    });

// Address fields - Alphanumeric + common punctuation
const validKYCAddress = (field: PropertyPath) =>
    requiredNonEmptyStringWithOptions(field, {
        customValidator: (value) => /^[A-Za-z0-9\s\-\.\,\#\/]+$/.test(value),
        errorMessage: 'Invalid address format. Only letters, numbers, spaces, and common punctuation are allowed',
    });

// Business/Company names - Extended character set
const validKYCBusinessName = (field: PropertyPath) =>
    requiredNonEmptyStringWithOptions(field, {
        customValidator: (value) => /^[A-Za-z0-9\s\-\.\,\#\/\&\(\)]+$/.test(value),
        errorMessage: 'Invalid business name format',
    });

// Alphanumeric fields - General purpose
const validKYCAlphanumeric = (field: PropertyPath) =>
    requiredNonEmptyStringWithOptions(field, {
        customValidator: (value) => /^[A-Za-z0-9\s\-]+$/.test(value),
        errorMessage: 'Invalid format. Only letters, numbers, spaces, and hyphens are allowed',
    });

// General string validator - Basic whitespace rejection
const validKYCString = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const input = trim(value);
        
        // Check for whitespace-only input or empty string
        if (whiteSpace.test(input) || empty.test(input)) {
            return context.defaultMessages.requiredValue;
        }
        
        return null;
    });
```

### 2. KYC Form Validator Updates (src/app/utilities/kycPresets/useKYCValidators.tsx)

#### 2.1 Field Category Mapping
**Priority 1: Name Fields (6 fields)**
- `FirstName`, `LastName`, `FullName`, `LastNameJapan`, `FirstNameJapan`, `LastNameFront`
- **Change**: `requiredString` → `validKYCName`
- **Impact**: Unicode character support, whitespace rejection

**Priority 2: Address Fields (9 fields)**
- `Address`, `City`, `District`, `Road`, `UnitNumber`, `CorrespondenceAddress`, etc.
- **Change**: `requiredString` → `validKYCAddress`
- **Impact**: Address-specific character validation, whitespace rejection

**Priority 3: Business Fields (4 fields)**
- `CompanyName`, `BusinessTitle`, `Occupation`, `CorporateName`
- **Change**: `requiredString` → `validKYCBusinessName`
- **Impact**: Business name character support, whitespace rejection

**Priority 4: Document Fields (1 field)**
- `Passport` (when required for non-citizens)
- **Change**: `requiredString` → `validKYCAlphanumeric`
- **Impact**: Alphanumeric validation, whitespace rejection

**Priority 5: General Fields (30+ fields)**
- Various string inputs with basic requirements
- **Change**: `requiredString` → `validKYCString`
- **Impact**: Basic whitespace rejection

#### 2.2 Implementation Pattern
```typescript
// Before (current pattern)
case LocalCustomerFieldKey.FirstName:
    return validators.only(() => kyc.isRequired, validators.requiredString(`${prefixName}.value`));

// After (enhanced pattern)
case LocalCustomerFieldKey.FirstName:
    return validators.only(() => kyc.isRequired, validators.validKYCName(`${prefixName}.value`));
```

### 3. Trimming Logic Expansion (src/app/utilities/kycPresets/index.tsx)

#### 3.1 Current State
```typescript
export const fieldsToRemoveWhitespace = [
    LocalCustomerFieldKey.Email,
    LocalCustomerFieldKey.IdentityNumber,
    LocalCustomerFieldKey.Phone,
    LocalCustomerFieldKey.Telephone,
    LocalCustomerFieldKey.CompanyPhone,
    LocalCustomerFieldKey.CorporatePhone,
    LocalCustomerFieldKey.CompanyPhoneExtension,
    LocalCustomerFieldKey.PostalCode,
    LocalCustomerFieldKey.CurrentVehicleRegistrationNumber,
    LocalCustomerFieldKey.CurrentVehicleVin,
]; // 13 fields total
```

#### 3.2 Enhanced State
```typescript
export const fieldsToRemoveWhitespace = [
    // Existing fields (13)
    LocalCustomerFieldKey.Email,
    LocalCustomerFieldKey.IdentityNumber,
    LocalCustomerFieldKey.Phone,
    LocalCustomerFieldKey.Telephone,
    LocalCustomerFieldKey.CompanyPhone,
    LocalCustomerFieldKey.CorporatePhone,
    LocalCustomerFieldKey.CompanyPhoneExtension,
    LocalCustomerFieldKey.PostalCode,
    LocalCustomerFieldKey.CurrentVehicleRegistrationNumber,
    LocalCustomerFieldKey.CurrentVehicleVin,
    
    // Name fields (6)
    LocalCustomerFieldKey.FirstName,
    LocalCustomerFieldKey.LastName,
    LocalCustomerFieldKey.FullName,
    LocalCustomerFieldKey.LastNameJapan,
    LocalCustomerFieldKey.FirstNameJapan,
    LocalCustomerFieldKey.LastNameFront,
    
    // Address fields (9)
    LocalCustomerFieldKey.Address,
    LocalCustomerFieldKey.City,
    LocalCustomerFieldKey.District,
    LocalCustomerFieldKey.Road,
    LocalCustomerFieldKey.UnitNumber,
    LocalCustomerFieldKey.CorrespondenceAddress,
    LocalCustomerFieldKey.CorrespondenceCity,
    LocalCustomerFieldKey.CorrespondenceDistrict,
    
    // Business fields (4)
    LocalCustomerFieldKey.CompanyName,
    LocalCustomerFieldKey.CompanyCity,
    LocalCustomerFieldKey.CompanyDistrict,
    LocalCustomerFieldKey.CompanyAddress,
    LocalCustomerFieldKey.BusinessTitle,
    LocalCustomerFieldKey.Occupation,
    LocalCustomerFieldKey.CorporateName,
    LocalCustomerFieldKey.CorporateIdentityNumber,
    
    // Document fields (1)
    LocalCustomerFieldKey.Passport,
    
    // General string fields (20+)
    LocalCustomerFieldKey.Title,
    LocalCustomerFieldKey.NonBinaryTitle,
    LocalCustomerFieldKey.Salutation,
    LocalCustomerFieldKey.SalutationBmw,
    LocalCustomerFieldKey.Nationality,
    LocalCustomerFieldKey.Country,
    LocalCustomerFieldKey.Citizenship,
    LocalCustomerFieldKey.Race,
    LocalCustomerFieldKey.Gender,
    LocalCustomerFieldKey.MaritalStatus,
    LocalCustomerFieldKey.ResidentialStatus,
    LocalCustomerFieldKey.Region,
    LocalCustomerFieldKey.EmploymentStatus,
    LocalCustomerFieldKey.CorporateIndustryCategory,
    LocalCustomerFieldKey.Education,
    LocalCustomerFieldKey.Emirate,
    LocalCustomerFieldKey.IncomeType,
    // ... additional fields as needed
]; // 50+ fields total
```

### 4. Error Messages (public/locales/en/common.json)

#### 4.1 New Error Codes
```json
{
    "formErrors": {
        // ... existing errors
        "whitespaceOnlyInput": "This field cannot contain only spaces",
        "invalidNameFormat": "Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed",
        "invalidAddressFormat": "Invalid address format. Only letters, numbers, spaces, and common punctuation are allowed",
        "invalidBusinessNameFormat": "Invalid business name format",
        "invalidAlphanumericFormat": "Invalid format. Only letters, numbers, spaces, and hyphens are allowed",
        "invalidFormat": "Invalid format"
    }
}
```

## Testing Strategy

### Unit Tests (src/app/utilities/__tests__/validators.test.ts)
```typescript
describe('KYC Field Validation', () => {
    describe('validKYCName', () => {
        it('should accept valid names', () => {
            expect(validateField('John')).toBe(null);
            expect(validateField('Mary-Jane')).toBe(null);
            expect(validateField("O'Connor")).toBe(null);
            expect(validateField('José María')).toBe(null); // Unicode
        });
        
        it('should reject whitespace-only inputs', () => {
            expect(validateField('   ')).toBe('requiredValue');
            expect(validateField('\t\t')).toBe('requiredValue');
            expect(validateField(' \n ')).toBe('requiredValue');
        });
        
        it('should trim and accept valid input', () => {
            expect(validateField('  John  ')).toBe(null);
        });
    });
    
    // Similar tests for other validators...
});
```

### Integration Tests
- Form submission with whitespace-only fields
- Validation error display
- Trimming behavior verification
- Conditional field validation

## Risk Mitigation

### Backward Compatibility
- Existing valid data remains valid
- Enhanced validators are more permissive than restrictive
- Gradual rollout by field priority

### Performance Considerations
- Regex patterns optimized for performance
- Validation functions execute in < 10ms
- No impact on form rendering performance

### Error Handling
- Graceful degradation if validation fails
- Clear error messages for users
- Consistent error code patterns

## Success Metrics

### Functional Verification
- [ ] All mandatory KYC fields reject whitespace-only inputs
- [ ] Optional fields accept empty OR valid non-whitespace content
- [ ] Input trimming applied consistently
- [ ] Field-specific validation rules work correctly
- [ ] International characters supported in name fields
- [ ] Existing valid customer data remains unaffected

### Technical Verification
- [ ] Enhanced validators created and exported
- [ ] Error codes follow existing translation patterns
- [ ] Validation applied consistently across all KYC forms
- [ ] Performance impact < 50ms additional validation time

## Implementation Timeline

### Phase 1: Core Validators (Day 1)
- Create enhanced validators in `validators.ts`
- Add new error messages to `common.json`
- Unit tests for core validation logic

### Phase 2: Form Integration (Day 2)
- Update `useKYCValidators.tsx` for Priority 1 fields (names)
- Update `useKYCValidators.tsx` for Priority 2 fields (addresses)
- Integration testing

### Phase 3: Extended Implementation (Day 3)
- Update Priority 3 & 4 fields (business, documents)
- Expand trimming logic in `index.tsx`
- Comprehensive testing

### Phase 4: Finalization (Day 4)
- Update remaining general fields
- Performance optimization
- QA verification

## Next Steps

1. **Implementation**: Begin Phase 1 - Create enhanced validators
2. **Testing**: Execute unit tests for each validator
3. **Integration**: Update KYC form validators incrementally
4. **Verification**: Test with actual forms in development environment
5. **Documentation**: Update technical documentation

This implementation plan ensures systematic enhancement of KYC field validation while maintaining system stability and user experience.
