import fetch, { Response } from 'node-fetch';
import config from '../../core/config';
import handleInvalidResponse from './handleInvalidResponse';

type Geometry = {
    type: string;
    coordinates: [number, number];
};

type Feature = {
    id: string;
    type: string;
    geometry: Geometry;
};

type CoordinateResponse = {
    type: string;
    features: Feature[];
};

const getGeocodingUrl = (searchText: string, countryCode: string) => {
    const { mapboxApi } = config;

    return `${mapboxApi.url}${encodeURIComponent(
        searchText
    )}.json?access_token=${mapboxApi.accessToken}&country=${countryCode}`;
};

const getGeometry = async (searchText: string, countryCode: string): Promise<Geometry> => {
    const endpoint = getGeocodingUrl(searchText, countryCode);
    const response: Response = await fetch(endpoint, { method: 'GET' });

    if (!response.ok) {
        await handleInvalidResponse(response);
    }

    const data: CoordinateResponse = await response.json();

    return data.features?.length > 0 ? data.features[0].geometry : null;
};

export default getGeometry;
