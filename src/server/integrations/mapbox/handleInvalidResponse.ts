import * as Sentry from '@sentry/node';
import { Response } from 'node-fetch';

/* handle invalid response from mapbox */
const handleInvalidResponse = async (response: Response): Promise<void> => {
    const errorMessage = 'Invalid response from mapbox api';
    const responseBody = await response.text();

    console.error(errorMessage, responseBody);

    Sentry.withScope(scope => {
        scope.setContext('mapboxResponse', { response: responseBody });
        Sentry.captureMessage(errorMessage);
    });

    throw new Error(errorMessage);
};

export default handleInvalidResponse;
