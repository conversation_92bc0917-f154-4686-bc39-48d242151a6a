import styled from 'styled-components';

const FieldLabel = styled.div`
    margin-bottom: 4px;
`;

const FieldValue = styled.div`
    border: 2px solid #6b6d70;
    border-radius: 4px;
    padding: 15px 16px;
    display: flex;
    align-items: center;
`;

interface FieldProps {
    label: JSX.Element | React.ReactNode;
    value: JSX.Element | React.ReactNode;
    suffix?: JSX.Element | React.ReactNode | null;
}
const Field = ({ label, value, suffix }: FieldProps) => (
    <div>
        <FieldLabel>{label}</FieldLabel>
        <FieldValue>
            {value}
            {suffix ? ` ${suffix}` : ''}
        </FieldValue>
    </div>
);

export default Field;
