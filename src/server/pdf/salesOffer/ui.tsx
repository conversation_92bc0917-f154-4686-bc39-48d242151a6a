import styled from 'styled-components';

export const PDFContainer = styled.div`
    display: flex;
    margin: 0;
    flex-direction: column;
    padding: 33px 24px;
    background-color: #fff;
`;

export const HeaderLogo = styled.div`
    margin: auto;
    margin-bottom: 33px;
    margin-top: 0px;
    height: auto;
    width: 240px;
    text-align: center;
    img {
        max-width: 240px;
        object-fit: contain;
        margin: 0;
        padding: 0;
    }
`;

export const ContentHeader = styled.div`
    margin: 0;
    margin-bottom: 16px;
    font-weight: 600;
    font-size: 28.3px;
`;

export const ContentBody = styled.div`
    display: flex;
    flex-direction: column;
    gap: 44px;
`;

export const SectionHeader = styled.div`
    margin: 0;
    margin-bottom: 16px;
    font-weight: 600;
    font-size: 20px;
`;

export const FieldContainer = styled.div`
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 16px;
`;
