import { useTranslation } from 'react-i18next';
import { SalesOffer, Customer, KYCField, Company } from '../../database';
import { Options } from '../../utils/translateOptions';
import CustomerField from '../components/application/ApplicantDetails/CustomerField';
import ConsentAndDeclarationDetails, {
    ConsentAndDeclarationsProps,
} from '../components/application/ConsentAndDeclaration';
import Signing from '../components/application/Signing';
import Field from './Field';
import V3PDFLayout from './V3PDFLayout';
import { PDFContainer, HeaderLogo, FieldContainer, SectionHeader, ContentHeader, ContentBody } from './ui';
import useCustomerData from './useCustomerData';

export type COEBiddingAgreementPdfProps = {
    companyLogo: string;
    salesOffer: SalesOffer;
    applicant: Customer;
    kycFields: KYCField[];
    company: Company;
    options: Options;
    agreements: ConsentAndDeclarationsProps['agreements'];
    signingProvider?: 'namirial' | 'docusign';
};
const COEBiddingAgreementPdf = ({
    salesOffer,
    companyLogo,
    applicant,
    kycFields,
    company,
    options,
    agreements,
    signingProvider,
}: COEBiddingAgreementPdfProps) => {
    const { t } = useTranslation(['saleOfferPdf', 'applicationPdf']);

    const { customerData, orderedKycFields } = useCustomerData(applicant, kycFields);

    return (
        <V3PDFLayout>
            <PDFContainer>
                <HeaderLogo>
                    <img alt="logo" src={companyLogo} />
                </HeaderLogo>
                <ContentHeader>{t('saleOfferPdf:coeBidding.title')}</ContentHeader>
                <ContentBody>
                    <div>
                        <FieldContainer>
                            <Field
                                label={t('saleOfferPdf:coeBidding.fields.serialNo')}
                                value={salesOffer.vsaSerialNumber}
                            />
                        </FieldContainer>
                    </div>
                    {orderedKycFields.length > 0 && (
                        <div>
                            <SectionHeader>
                                {t('saleOfferPdf:coeBidding.sectionTitle.purchaseParticulars')}
                            </SectionHeader>
                            <FieldContainer>
                                {(orderedKycFields || []).map(field => (
                                    <CustomerField
                                        key={field.key}
                                        Component={Field}
                                        data={customerData}
                                        fieldKey={field.key}
                                        options={options}
                                        timeZone={company.timeZone}
                                    />
                                ))}
                            </FieldContainer>
                        </div>
                    )}
                    <div>
                        <SectionHeader>{t('saleOfferPdf:coeBidding.sectionTitle.TableOfParticulars')}</SectionHeader>
                        <FieldContainer>
                            <Field
                                label={t('saleOfferPdf:coeBidding.fields.coeCategory')}
                                value={salesOffer.mainDetails.coeCategory}
                            />
                            <Field
                                label={t('saleOfferPdf:coeBidding.fields.NoOfConsecutiveBids')}
                                value={salesOffer.mainDetails.numberConsecutiveBids}
                            />
                        </FieldContainer>
                    </div>
                    <div style={{ margin: '0 -10px', marginTop: '-30px' }}>
                        <ConsentAndDeclarationDetails
                            agreements={agreements}
                            hideFinancingConsent
                            hideInsuranceConsent
                        />
                    </div>
                    {!!signingProvider && (
                        <div style={{ margin: '0 -5px', marginTop: '-35px' }}>
                            <Signing
                                hasGuarantor={false}
                                label={t('saleOfferPdf:coeBidding.signingLabel')}
                                provider={signingProvider}
                            />
                        </div>
                    )}
                </ContentBody>
            </PDFContainer>
        </V3PDFLayout>
    );
};

export default COEBiddingAgreementPdf;
