import { useMemo } from 'react';
import { Customer, KYCField, getLocalCustomerAggregatedFields, KycFieldPurpose } from '../../database';
import {
    EmploymentDetailsKey,
    AddressDetailsKey,
    CorrespondenceKey,
    CustomerDetailsKey,
    OthersKey,
    CorporateInformationKey,
    IdentityDetailsKey,
    ReferenceDetailsKey,
} from '../components/application/ApplicantDetails/shared';

const useCustomerData = (applicant: Customer, kycFields: KYCField[]) => {
    const data = getLocalCustomerAggregatedFields(applicant);

    const orderedKycFields = useMemo(() => {
        const visibleKycFields = {
            customerDetails: [],
            addressDetails: [],
            correspondenceAddress: [],
            corporateInformation: [],
            employmentDetails: [],
            identityDetails: [],
            referenceDetails: [],
            others: [],
        };

        (kycFields || []).forEach(kyc => {
            if (kyc.purpose.includes(KycFieldPurpose.KYC)) {
                if (CustomerDetailsKey.includes(kyc.key)) {
                    visibleKycFields.customerDetails.push({ key: kyc.key });
                }

                if (EmploymentDetailsKey.includes(kyc.key)) {
                    visibleKycFields.employmentDetails.push({ key: kyc.key });
                }

                if (CorporateInformationKey.includes(kyc.key)) {
                    visibleKycFields.corporateInformation.push({ key: kyc.key });
                }

                if (AddressDetailsKey.includes(kyc.key)) {
                    visibleKycFields.addressDetails.push({ key: kyc.key });
                }

                if (IdentityDetailsKey.includes(kyc.key)) {
                    visibleKycFields.identityDetails.push({ key: kyc.key });
                }

                if (ReferenceDetailsKey.includes(kyc.key)) {
                    visibleKycFields.referenceDetails.push({ key: kyc.key });
                }

                if (CorrespondenceKey.includes(kyc.key)) {
                    visibleKycFields.correspondenceAddress.push({ key: kyc.key });
                }

                if (OthersKey.includes(kyc.key)) {
                    visibleKycFields.others.push({ key: kyc.key });
                }
            }
        });

        return [
            ...visibleKycFields.customerDetails,
            ...visibleKycFields.addressDetails,
            ...visibleKycFields.correspondenceAddress,
            ...visibleKycFields.corporateInformation,
            ...visibleKycFields.employmentDetails,
            ...visibleKycFields.identityDetails,
            ...visibleKycFields.referenceDetails,
            ...visibleKycFields.others,
        ];
    }, [kycFields]);

    return {
        customerData: data,
        orderedKycFields,
    };
};

export default useCustomerData;
