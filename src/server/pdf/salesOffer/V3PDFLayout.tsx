import { ReactNode } from 'react';
import getPublic from '../../emails/utils/getPublic';

export type V3LayoutProps = {
    children: JSX.Element | ReactNode;
};

const V3PDFLayout = ({ children }: V3LayoutProps) => {
    const regular = getPublic('fonts/PorscheNext/PorscheNext-Regular.otf');
    const regularItalic = getPublic('fonts/PorscheNext/PorscheNext-RegularItalic.otf');
    const bold = getPublic('fonts/PorscheNext/PorscheNext-Bold.otf');
    const boldItalic = getPublic('fonts/PorscheNext/PorscheNext-BoldItalic.otf');
    const semiBold = getPublic('fonts/PorscheNext/PorscheNext-SemiBold.otf');
    const semiBoldItalic = getPublic('fonts/PorscheNext/PorscheNext-SemiBoldItalic.otf');
    const thin = getPublic('fonts/PorscheNext/PorscheNext-Thin.otf');
    const thinItalic = getPublic('fonts/PorscheNext/PorscheNext-ThinItalic.otf');

    return (
        <html lang="en">
            <head>
                <style
                    // eslint-disable-next-line react/no-danger
                    dangerouslySetInnerHTML={{
                        __html: `
                            @page {
                                size: A4;
                                margin: 0.75cm 0;
                            }

                            @font-face {
                                font-family: 'PorscheNext';
                                src: url('${regular}') format('opentype');
                                font-weight: normal;
                                font-style: normal;
                            }
                            @font-face {
                                font-family: 'PorscheNext';
                                src: url('${regularItalic}') format('opentype');
                                font-weight: normal;
                                font-style: italic;
                            }

                            @font-face {
                                font-family: 'PorscheNext';
                                src: url('${bold}') format('opentype');
                                font-weight: 700;
                                font-style: normal;
                            }
                            @font-face {
                                font-family: 'PorscheNext';
                                src: url('${boldItalic}') format('opentype');
                                font-weight: 700;
                                font-style: italic;
                            }

                            @font-face {
                                font-family: 'PorscheNext';
                                src: url('${semiBold}') format('opentype');
                                font-weight: 600;
                                font-style: normal;
                            }
                            @font-face {
                                font-family: 'PorscheNext';
                                src: url('${semiBoldItalic}') format('opentype');
                                font-weight: 600;
                                font-style: italic;
                            }

                            @font-face {
                                font-family: 'PorscheNext';
                                src: url('${thin}') format('opentype');
                                font-weight: 100;
                                font-style: normal;
                            }
                            @font-face {
                                font-family: 'PorscheNext';
                                src: url('${thinItalic}') format('opentype');
                                font-weight: 100;
                                font-style: italic;
                            }

                            body {
                                font-family: 'PorscheNext', sans-serif;
                                font-size: 16px;
                                font-weight: normal;
                                color: #010205;
                            }
                        `,
                    }}
                />
            </head>
            <body style={{ margin: 0, padding: 0 }}>
                <div>{children}</div>
            </body>
        </html>
    );
};

export default V3PDFLayout;
