import { ModuleType, SalesOfferModule } from '../documents';
import type { DatabaseContext } from '../getDatabaseContext';

export default {
    identifier: '283_removeSalesOfferModuleCustomerModule',

    async up({ encrypted: { db } }: DatabaseContext): Promise<void> {
        await db
            .collection<SalesOfferModule>('modules')
            .updateMany({ _type: ModuleType.SalesOfferModule }, { $unset: { customerModuleId: 1 } });
    },
};
