import { getUrlForUpload } from '../../../core/storage';
import type { Collections } from '../../../database/collections';
import {
    type Application,
    type ApplicationDocument,
    ApplicationDocumentKind,
    AuthorKind,
    ConsentsAndDeclarations,
    type Lead,
    ModuleType,
    type SalesOffer,
    SalesOfferApplication,
    type SalesOfferDocument,
    SalesOfferDocumentKind,
    SalesOfferFeatureKind,
    SalesOfferFeatureStatus,
} from '../../../database/documents';
import getAuthorDetails from '../../../database/helpers/getAuthorDetails';
import getAuthorName from '../../../database/helpers/getAuthorName';
import { getKycFieldsFromSalesOffer } from '../../../database/helpers/kyc';
import { createFormatAmountFn } from '../../../emails/utils/useFormats';
import getCompanyFontInBase64 from '../../../journeys/helper/getCompanyFontInBase64';
import getSigningProvider from '../../../journeys/helper/getSigningProvider';
import { createInsurancePdf } from '../../../journeys/helper/uploadInsuranceAgreementPdf';
import { Loaders } from '../../../loaders';
import { renderInsuranceApplicationPDF } from '../../../pdf';
import createI18nInstance from '../../../utils/createI18nInstance';
import ensureManyFromLoaders from '../../../utils/ensureManyFromLoaders';
import { translateOptions } from '../../../utils/translateOptions';
import { getSimpleVersioningForUpdate } from '../../../utils/versioning';
import { mainQueue } from '../../mainQueue';

export const attachInsuranceDocument = async (
    collections: Collections,
    salesOffer: SalesOffer,
    application: Application,
    lead: Lead,
    agreementPdf: Buffer
) => {
    // create agreement pdf document
    const uploadedFile = await createInsurancePdf(agreementPdf, application, lead);
    const document: ApplicationDocument = {
        kind: ApplicationDocumentKind.InsuranceAgreement,
        applicationId: application._id,
        ...uploadedFile,
    };

    // push the document on the application
    await collections.applications.findOneAndUpdate(
        { kind: application.kind, _id: application._id },
        { $set: { ...getSimpleVersioningForUpdate({ kind: AuthorKind.System }) }, $push: { documents: document } },
        { returnDocument: 'after' }
    );

    // push the document on the sales offer
    const salesOfferDocument: SalesOfferDocument<SalesOfferDocumentKind.Insurance> = {
        kind: SalesOfferDocumentKind.Insurance,
        lastUpdatedAt: new Date(),
        status: SalesOfferFeatureStatus.PendingCustomer,
        ...uploadedFile,
    };
    await collections.salesOffers.findOneAndUpdate(
        { _id: salesOffer._id },
        {
            $push: { 'insurance.documents': salesOfferDocument },
        },
        { returnDocument: 'after' }
    );

    // trigger the job for generating preview
    await mainQueue.add({
        type: 'generatePreviewOnApplicationDocument',
        applicationSuiteId: application._versioning.suiteId,
        uploadedFileId: document._id,
    });
};

export const generateInsurancePdf = async (
    salesOffer: SalesOffer,
    application: SalesOfferApplication,
    loaders: Loaders
) => {
    const { applicantId, insurancing, moduleId, vehicleId } = application;

    const salesOfferModule = await loaders.moduleById.load(moduleId);
    if (salesOfferModule._type !== ModuleType.SalesOfferModule) {
        throw new Error('Application is not for insurance');
    }

    const [applicant, bank, insurer, journey, vehicle, insuranceProduct, lead, dealer] = await Promise.all([
        loaders.customerById.load(applicantId),
        application.bankId ? loaders.bankById.load(application.bankId) : null,
        loaders.insurerById.load(insurancing.insurerId),
        loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId),
        loaders.vehicleById.load(vehicleId),
        loaders.insuranceProductById.load(insurancing.insuranceProductId),
        loaders.leadById.load(application.leadId),
        loaders.dealerById.load(application.dealerId),
    ]);

    const launchPadModule = await loaders.moduleById.load(lead.moduleId);
    if (launchPadModule._type !== ModuleType.LaunchPadModule) {
        throw new Error('Application is not for insurance');
    }

    // sales offer application use customer module from launch pad
    const customerModule = await loaders.customerModuleById.load(launchPadModule.customerModuleId);

    const kycFields = await getKycFieldsFromSalesOffer(salesOffer, SalesOfferFeatureKind.Insurance, null, loaders);

    const company = await loaders.companyById.load(salesOfferModule.companyId);

    // C&D document
    const agreedConsentIds = journey.applicantAgreements.agreements
        .filter(agreement => agreement.isAgreed === true)
        .map(agreement => agreement.consentId);

    const consentAndDeclarations = await loaders.consentById
        .loadMany(agreedConsentIds)
        .then(ensureManyFromLoaders<ConsentsAndDeclarations>);

    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'applicationPdf', 'calculators']);

    const { t } = i18n;
    const options = translateOptions(t);

    const signingModule = await loaders.moduleById.load(salesOfferModule.signingModuleId);
    const signingProvider = getSigningProvider(signingModule);

    const helpers = {
        formatAmountWithCurrency: createFormatAmountFn(company.roundings.amount.decimals, company.currency),
    };

    const user = await getAuthorDetails(application._versioning.createdBy);

    return renderInsuranceApplicationPDF(
        {
            companyLogo: company.logo ? await getUrlForUpload(company.logo) : null,
            bank,
            company,
            application,
            applicationModule: salesOfferModule,
            lead,
            applicant,
            vehicle,
            agreements: journey.applicantAgreements.agreements.map(agreement => ({
                ...agreement,
                consent: consentAndDeclarations.find(consent => consent._id.equals(agreement.consentId)),
            })),
            createdBy: await getAuthorName(application._versioning.createdBy, t),
            creatorMobile: user?.phone,
            creatorReferenceCode: user?.referenceCode,
            helpers,
            dealer,
            dealerName: dealer.displayName,
            kycFields,
            options,
            signingProvider,
            insurer,
            fontUrlInBase64: await getCompanyFontInBase64(company),
            customerModule,
            insuranceProduct,
        },
        i18n
    );
};
