import { getUrlForUpload } from '../../../core/storage';
import type { Collections } from '../../../database/collections';
import {
    type Application,
    type ApplicationDocument,
    ApplicationDocumentKind,
    ApplicationKind,
    AuthorKind,
    type ConsentsAndDeclarations,
    type Lead,
    ModuleType,
    type SalesOffer,
    type SalesOfferDocument,
    SalesOfferDocumentKind,
    SalesOfferFeatureKind,
    SalesOfferFeatureStatus,
} from '../../../database/documents';
import getAuthorDetails from '../../../database/helpers/getAuthorDetails';
import getAuthorName from '../../../database/helpers/getAuthorName';
import { getKycFieldsFromSalesOffer } from '../../../database/helpers/kyc';
import { createFormatAmountFn } from '../../../emails/utils/useFormats';
import { uploadAgreementPdf } from '../../../journeys/helper/createAgreementPdf';
import getCompanyFontInBase64 from '../../../journeys/helper/getCompanyFontInBase64';
import getSigningProvider from '../../../journeys/helper/getSigningProvider';
import type { Loaders } from '../../../loaders';
import { renderApplicationPDF } from '../../../pdf';
import createI18nInstance from '../../../utils/createI18nInstance';
import ensureManyFromLoaders from '../../../utils/ensureManyFromLoaders';
import { translateOptions } from '../../../utils/translateOptions';
import { getSimpleVersioningForUpdate } from '../../../utils/versioning';
import { mainQueue } from '../../mainQueue';

export const attachAgreementDocument = async (
    collections: Collections,
    salesOffer: SalesOffer,
    application: Application,
    lead: Lead,
    agreementPdf: Buffer
) => {
    // create agreement pdf document
    const uploadedFile = await uploadAgreementPdf(agreementPdf, application, lead);
    const document: ApplicationDocument = {
        kind: ApplicationDocumentKind.Agreement,
        applicationId: application._id,
        ...uploadedFile,
    };

    // push the document on the application
    await collections.applications.findOneAndUpdate(
        { kind: application.kind, _id: application._id },
        { $set: { ...getSimpleVersioningForUpdate({ kind: AuthorKind.System }) }, $push: { documents: document } },
        { returnDocument: 'after' }
    );

    // push the document on the sales offer
    const salesOfferDocument: SalesOfferDocument<SalesOfferDocumentKind.Finance> = {
        kind: SalesOfferDocumentKind.Finance,
        lastUpdatedAt: new Date(),
        status: SalesOfferFeatureStatus.PendingCustomer,
        ...uploadedFile,
    };
    await collections.salesOffers.findOneAndUpdate(
        { _id: salesOffer._id },
        {
            $push: { 'finance.documents': salesOfferDocument },
        },
        { returnDocument: 'after' }
    );

    // trigger the job for generating preview
    await mainQueue.add({
        type: 'generatePreviewOnApplicationDocument',
        applicationSuiteId: application._versioning.suiteId,
        uploadedFileId: document._id,
    });
};

export const generateFinancingPdf = async (salesOffer: SalesOffer, application: Application, loaders: Loaders) => {
    if (application.kind !== ApplicationKind.SalesOffer) {
        throw new Error('Application not supported for sales offer financing PDF');
    }

    const { applicantId, vehicleId } = application;

    const salesOfferModule = await loaders.moduleById.load(application.moduleId);
    if (salesOfferModule._type !== ModuleType.SalesOfferModule) {
        throw new Error('Invalid application module for sales offer financing PDF');
    }

    const [vehicle, applicant, company, bank, financeProduct, journey, lead, dealer, signingModule] = await Promise.all(
        [
            vehicleId ? loaders.vehicleById.load(vehicleId) : null,
            loaders.customerById.load(applicantId),
            loaders.companyById.load(salesOfferModule.companyId),
            application.bankId ? loaders.bankById.load(application.bankId) : null,
            application.financing ? loaders.financeProductById.load(application.financing.financeProductId) : null,
            loaders.applicationJourneyBySuiteId.load(application._versioning.suiteId),
            loaders.leadById.load(application.leadId),
            loaders.dealerById.load(application.dealerId),
            loaders.moduleById.load(salesOfferModule.signingModuleId),
        ]
    );

    const launchPadModule = await loaders.moduleById.load(lead.moduleId);
    if (launchPadModule._type !== ModuleType.LaunchPadModule) {
        throw new Error('Invalid application module for sales offer financing PDF');
    }

    // sales offer application use customer module from launch pad
    const kycFields = await getKycFieldsFromSalesOffer(salesOffer, SalesOfferFeatureKind.Finance, null, loaders);

    const agreedConsentIds = journey.applicantAgreements.agreements
        .filter(agreement => agreement.isAgreed === true)
        .map(agreement => agreement.consentId);

    const consentAndDeclaration = await loaders.consentById
        .loadMany(agreedConsentIds)
        .then(ensureManyFromLoaders<ConsentsAndDeclarations>);

    // load translations
    const { i18n } = await createI18nInstance(application.languageId?.toHexString() ?? null);
    await i18n.loadNamespaces(['common', 'applicationPdf', 'calculators']);

    const { t } = i18n;
    const options = translateOptions(t);

    const helpers = {
        formatAmountWithCurrency: createFormatAmountFn(company.roundings.amount.decimals, company.currency),
    };
    const user = await getAuthorDetails(application._versioning.createdBy);

    const signingProvider = getSigningProvider(signingModule);

    return renderApplicationPDF(
        {
            companyLogo: company.logo ? await getUrlForUpload(company.logo) : null,
            company,
            application,
            applicationModule: salesOfferModule,
            applicant,
            vehicle,
            bank,
            financeProduct,
            lead,
            agreements: journey.applicantAgreements.agreements.map(agreement => ({
                ...agreement,
                consent: consentAndDeclaration.find(consent => consent._id.equals(agreement.consentId)),
            })),
            createdBy: await getAuthorName(application._versioning.createdBy, t),
            creatorMobile: user?.phone,
            creatorReferenceCode: user?.referenceCode,
            helpers,
            dealer,
            dealerId: application.dealerId,
            kycFields,
            options,
            signingProvider,
            fontUrlInBase64: await getCompanyFontInBase64(company),
        },
        i18n
    );
};
