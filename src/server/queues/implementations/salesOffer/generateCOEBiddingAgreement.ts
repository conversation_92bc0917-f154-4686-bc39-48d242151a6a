import { ObjectId } from 'mongodb';
import { getUrlForUpload } from '../../../core/storage';
import {
    SalesOffer,
    LaunchpadLead,
    SalesOfferModule,
    ConsentsAndDeclarations,
    getKycFieldsFromSalesOffer,
    SalesOfferFeatureKind,
    SalesOfferAgreementKind,
} from '../../../database';
import getSigningProvider from '../../../journeys/helper/getSigningProvider';
import { Loaders } from '../../../loaders';
import { renderCOEBiddingAgreementPdf } from '../../../pdf';
import createI18nInstance from '../../../utils/createI18nInstance';
import ensureManyFromLoaders from '../../../utils/ensureManyFromLoaders';
import { translateOptions } from '../../../utils/translateOptions';

type GenerateCOEBiddingAgreementParams = {
    languageId: ObjectId;
    salesOffer: SalesOffer;
    lead: LaunchpadLead;
    loaders: Loaders;
};

const generateCOEBiddingAgreement = async ({
    languageId,
    salesOffer,
    lead,
    loaders,
}: GenerateCOEBiddingAgreementParams) => {
    // load translations
    const { i18n } = await createI18nInstance(languageId?.toHexString() ?? null);

    await i18n.loadNamespaces(['common', 'saleOfferPdf', 'applicationPdf']);

    const { t } = i18n;
    const options = translateOptions(t);

    const salesOfferModule = (await loaders.moduleById.load(salesOffer.moduleId)) as SalesOfferModule;
    const signingModule = await loaders.moduleById.load(salesOfferModule.signingModuleId);
    const signingProvider = getSigningProvider(signingModule);

    const company = await loaders.companyById.load(salesOfferModule.companyId);

    const applicant = await loaders.customerById.load(lead.customerId);

    const kycFields = await getKycFieldsFromSalesOffer(
        salesOffer,
        SalesOfferFeatureKind.MainDetails,
        SalesOfferAgreementKind.COE
    );

    // get C&D document
    const coeConsent = salesOffer.consents?.coe.agreements ?? [];
    const agreedConsentIds = coeConsent.map(agreement => agreement.consentId);
    const consentAndDeclaration = await loaders.consentById
        .loadMany(agreedConsentIds)
        .then(ensureManyFromLoaders<ConsentsAndDeclarations>);

    return renderCOEBiddingAgreementPdf(
        {
            companyLogo: company.logo ? await getUrlForUpload(company.logo) : null,
            salesOffer,
            company,
            applicant,
            kycFields,
            options,
            agreements: coeConsent.map(agreement => ({
                ...agreement,
                consent: consentAndDeclaration.find(consent => consent._id.equals(agreement.consentId)),
            })),
            signingProvider,
        },
        i18n
    );
};

export default generateCOEBiddingAgreement;
