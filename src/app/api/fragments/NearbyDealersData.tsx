import type * as SchemaTypes from '../types';

import type { TranslatedStringDataFragment } from './TranslatedStringData';
import type { DealerContactFragmentFragment } from './DealerContactFragment';
import type { DealerSocialMediaFragmentFragment } from './DealerSocialMediaFragment';
import type { UploadFileWithPreviewFormDataFragment } from './UploadFileWithPreviewFormData';
import { gql } from '@apollo/client';
import { TranslatedStringDataFragmentDoc } from './TranslatedStringData';
import { DealerContactFragmentFragmentDoc } from './DealerContactFragment';
import { DealerSocialMediaFragmentFragmentDoc } from './DealerSocialMediaFragment';
import { UploadFileWithPreviewFormDataFragmentDoc } from './UploadFileWithPreviewFormData';
export type NearbyDealersDataFragment = (
  { __typename: 'Dealer' }
  & Pick<SchemaTypes.Dealer, 'id' | 'displayName' | 'calculatedDistance'>
  & { legalName: (
    { __typename: 'TranslatedString' }
    & TranslatedStringDataFragment
  ), contact: (
    { __typename: 'DealerContact' }
    & DealerContactFragmentFragment
  ) }
);

export const NearbyDealersDataFragmentDoc = /*#__PURE__*/ gql`
    fragment NearbyDealersData on Dealer {
  id
  displayName
  legalName {
    ...TranslatedStringData
  }
  contact {
    ...DealerContactFragment
  }
  calculatedDistance
}
    `;