import type * as SchemaTypes from '../types';

import type { KycFieldSpecsFragment } from './KYCFieldSpecs';
import { gql } from '@apollo/client';
import { KycFieldSpecsFragmentDoc } from './KYCFieldSpecs';
export type SalesOfferKycPresetSpecsFragment = (
  { __typename: 'SalesOfferKYCPreset' }
  & { finance: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, insurance: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, salesOffer: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, coe: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )>, specification: Array<(
    { __typename: 'KYCField' }
    & KycFieldSpecsFragment
  )> }
);

export const SalesOfferKycPresetSpecsFragmentDoc = /*#__PURE__*/ gql`
    fragment SalesOfferKYCPresetSpecs on SalesOfferKYCPreset {
  finance {
    ...KYCFieldSpecs
  }
  insurance {
    ...KYCFieldSpecs
  }
  salesOffer {
    ...KYCFieldSpecs
  }
  coe {
    ...KYCFieldSpecs
  }
  specification {
    ...KYCFieldSpecs
  }
}
    `;