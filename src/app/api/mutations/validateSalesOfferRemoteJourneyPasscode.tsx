import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type ValidateSalesOfferRemoteJourneyPasscodeMutationVariables = SchemaTypes.Exact<{
  secret: SchemaTypes.Scalars['String']['input'];
  code: SchemaTypes.Scalars['String']['input'];
}>;


export type ValidateSalesOfferRemoteJourneyPasscodeMutation = (
  { __typename: 'Mutation' }
  & { result: SchemaTypes.Mutation['validateSalesOfferRemoteJourneyPasscode'] }
);


export const ValidateSalesOfferRemoteJourneyPasscodeDocument = /*#__PURE__*/ gql`
    mutation validateSalesOfferRemoteJourneyPasscode($secret: String!, $code: String!) {
  result: validateSalesOfferRemoteJourneyPasscode(secret: $secret, code: $code)
}
    `;
export type ValidateSalesOfferRemoteJourneyPasscodeMutationFn = Apollo.MutationFunction<ValidateSalesOfferRemoteJourneyPasscodeMutation, ValidateSalesOfferRemoteJourneyPasscodeMutationVariables>;

/**
 * __useValidateSalesOfferRemoteJourneyPasscodeMutation__
 *
 * To run a mutation, you first call `useValidateSalesOfferRemoteJourneyPasscodeMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useValidateSalesOfferRemoteJourneyPasscodeMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [validateSalesOfferRemoteJourneyPasscodeMutation, { data, loading, error }] = useValidateSalesOfferRemoteJourneyPasscodeMutation({
 *   variables: {
 *      secret: // value for 'secret'
 *      code: // value for 'code'
 *   },
 * });
 */
export function useValidateSalesOfferRemoteJourneyPasscodeMutation(baseOptions?: Apollo.MutationHookOptions<ValidateSalesOfferRemoteJourneyPasscodeMutation, ValidateSalesOfferRemoteJourneyPasscodeMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<ValidateSalesOfferRemoteJourneyPasscodeMutation, ValidateSalesOfferRemoteJourneyPasscodeMutationVariables>(ValidateSalesOfferRemoteJourneyPasscodeDocument, options);
      }
export type ValidateSalesOfferRemoteJourneyPasscodeMutationHookResult = ReturnType<typeof useValidateSalesOfferRemoteJourneyPasscodeMutation>;
export type ValidateSalesOfferRemoteJourneyPasscodeMutationResult = Apollo.MutationResult<ValidateSalesOfferRemoteJourneyPasscodeMutation>;
export type ValidateSalesOfferRemoteJourneyPasscodeMutationOptions = Apollo.BaseMutationOptions<ValidateSalesOfferRemoteJourneyPasscodeMutation, ValidateSalesOfferRemoteJourneyPasscodeMutationVariables>;