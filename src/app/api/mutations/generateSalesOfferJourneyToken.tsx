import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
const defaultOptions = {} as const;
export type GenerateSalesOfferJourneyTokenMutationVariables = SchemaTypes.Exact<{
  secret: SchemaTypes.Scalars['String']['input'];
}>;


export type GenerateSalesOfferJourneyTokenMutation = (
  { __typename: 'Mutation' }
  & { result: SchemaTypes.Mutation['generateSalesOfferJourneyToken'] }
);


export const GenerateSalesOfferJourneyTokenDocument = /*#__PURE__*/ gql`
    mutation generateSalesOfferJourneyToken($secret: String!) {
  result: generateSalesOfferJourneyToken(secret: $secret)
}
    `;
export type GenerateSalesOfferJourneyTokenMutationFn = Apollo.MutationFunction<GenerateSalesOfferJourneyTokenMutation, GenerateSalesOfferJourneyTokenMutationVariables>;

/**
 * __useGenerateSalesOfferJourneyTokenMutation__
 *
 * To run a mutation, you first call `useGenerateSalesOfferJourneyTokenMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useGenerateSalesOfferJourneyTokenMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [generateSalesOfferJourneyTokenMutation, { data, loading, error }] = useGenerateSalesOfferJourneyTokenMutation({
 *   variables: {
 *      secret: // value for 'secret'
 *   },
 * });
 */
export function useGenerateSalesOfferJourneyTokenMutation(baseOptions?: Apollo.MutationHookOptions<GenerateSalesOfferJourneyTokenMutation, GenerateSalesOfferJourneyTokenMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<GenerateSalesOfferJourneyTokenMutation, GenerateSalesOfferJourneyTokenMutationVariables>(GenerateSalesOfferJourneyTokenDocument, options);
      }
export type GenerateSalesOfferJourneyTokenMutationHookResult = ReturnType<typeof useGenerateSalesOfferJourneyTokenMutation>;
export type GenerateSalesOfferJourneyTokenMutationResult = Apollo.MutationResult<GenerateSalesOfferJourneyTokenMutation>;
export type GenerateSalesOfferJourneyTokenMutationOptions = Apollo.BaseMutationOptions<GenerateSalesOfferJourneyTokenMutation, GenerateSalesOfferJourneyTokenMutationVariables>;