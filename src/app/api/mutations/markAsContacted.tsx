import type * as SchemaTypes from '../types';

import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
const defaultOptions = {} as const;
export type MarkAsContactedMutationVariables = SchemaTypes.Exact<{
  leadId: SchemaTypes.Scalars['ObjectID']['input'];
}>;


export type MarkAsContactedMutation = (
  { __typename: 'Mutation' }
  & { result: SchemaTypes.Mutation['markAsContacted'] }
);


export const MarkAsContactedDocument = /*#__PURE__*/ gql`
    mutation markAsContacted($leadId: ObjectID!) {
  result: markAsContacted(leadId: $leadId)
}
    `;
export type MarkAsContactedMutationFn = Apollo.MutationFunction<MarkAsContactedMutation, MarkAsContactedMutationVariables>;

/**
 * __useMarkAsContactedMutation__
 *
 * To run a mutation, you first call `useMarkAsContactedMutation` within a React component and pass it any options that fit your needs.
 * When your component renders, `useMarkAsContactedMutation` returns a tuple that includes:
 * - A mutate function that you can call at any time to execute the mutation
 * - An object with fields that represent the current status of the mutation's execution
 *
 * @param baseOptions options that will be passed into the mutation, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options-2;
 *
 * @example
 * const [markAsContactedMutation, { data, loading, error }] = useMarkAsContactedMutation({
 *   variables: {
 *      leadId: // value for 'leadId'
 *   },
 * });
 */
export function useMarkAsContactedMutation(baseOptions?: Apollo.MutationHookOptions<MarkAsContactedMutation, MarkAsContactedMutationVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useMutation<MarkAsContactedMutation, MarkAsContactedMutationVariables>(MarkAsContactedDocument, options);
      }
export type MarkAsContactedMutationHookResult = ReturnType<typeof useMarkAsContactedMutation>;
export type MarkAsContactedMutationResult = Apollo.MutationResult<MarkAsContactedMutation>;
export type MarkAsContactedMutationOptions = Apollo.BaseMutationOptions<MarkAsContactedMutation, MarkAsContactedMutationVariables>;