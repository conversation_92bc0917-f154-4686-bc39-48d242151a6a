import { useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { FinanceProductBasedOn, FpTableKind } from '../../../../../../api/types';
import { useAccountContext } from '../../../../../../components/contexts/AccountContextManager';
import { useThemeComponents } from '../../../../../../themes/hooks';
import exportFinanceProductTable from '../../../../../../utilities/export/financeProductTable';
import { useFinanceProductDetailContext } from '../../../FinanceProductDetailContext';
import { type FinanceProductFormValues } from '../../../shared/typings';
import useFinanceProductOptions from '../../../shared/useFinanceProductOptions';
import RandomTable from './RandomTable';
import StepTable from './StepTable';
import { TableRecord } from './types';

export type InterestRateTableProps = {
    disabled?: boolean;
};

const InterestRateTable = ({ disabled = false }: InterestRateTableProps) => {
    const { values } = useFormikContext<FinanceProductFormValues>();
    const { notification } = useThemeComponents();
    const { interestRate, basedOn, displayName } = values;
    const { type, isGenerateStepsInTable, unit } = interestRate || {};

    const { token } = useAccountContext();
    const { bank } = useFinanceProductDetailContext();
    const { amountUnitOptions } = useFinanceProductOptions(bank?.module?.companyId);

    const mappedHeaders = useMemo(() => {
        const basedOnKey = `${basedOn === FinanceProductBasedOn.DownPayment ? 'Downpayment' : 'Loan'} ${
            amountUnitOptions.find(item => item.value === unit)?.label
        }*`;

        return [
            { text: basedOnKey, key: 'base' },
            { text: 'Term*', key: 'term' },
        ];
    }, [amountUnitOptions, basedOn, unit]);

    const onExport = useCallback(
        async (records: TableRecord[]) => {
            try {
                await exportFinanceProductTable({
                    token,
                    productName: displayName,
                    tableValues: {
                        tableKind: FpTableKind.InterestRate,
                        headers: mappedHeaders,
                        records,
                    },
                });
            } catch (error) {
                notification.error(error);
            }
        },
        [displayName, mappedHeaders, notification, token]
    );

    const tableProps = {
        disabled,
        mappedHeaders,
        onExport,
    };

    if (type === 'table' && isGenerateStepsInTable) {
        return <StepTable {...tableProps} />;
    }

    return <RandomTable {...tableProps} />;
};

export default InterestRateTable;
