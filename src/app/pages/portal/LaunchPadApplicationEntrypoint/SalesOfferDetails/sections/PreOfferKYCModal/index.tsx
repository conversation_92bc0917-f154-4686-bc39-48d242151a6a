import { useMemo, useState } from 'react';
import { SalesOfferFeatureKind } from '../../../../../../api/types';
import Modal from './Modal';

const usePreOfferKYCModal = () => {
    const [open, setOpen] = useState(false);
    const [featureKindSelected, setFeatureKindSelected] = useState<Array<SalesOfferFeatureKind>>([]);

    const action = useMemo(
        () => ({
            open: (selectedKinds: Array<SalesOfferFeatureKind>) => {
                setFeatureKindSelected(selectedKinds);
                setOpen(true);
            },
            close: () => setOpen(false),
        }),
        []
    );

    return {
        ...action,
        render: () => <Modal featureKindSelected={featureKindSelected} open={open} setOpen={setOpen} />,
    };
};

export default usePreOfferKYCModal;
