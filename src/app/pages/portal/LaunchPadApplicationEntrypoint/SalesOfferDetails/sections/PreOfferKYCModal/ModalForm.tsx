import { ApolloError } from '@apollo/client';
import { Formik } from 'formik';
import { Dispatch, SetStateAction, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import {
    ApplicationAgreementDataFragment,
    KycFieldSpecsFragment,
    useSendSalesOfferMutation,
} from '../../../../../../api';
import { CustomerKind, SalesOfferFeatureKind } from '../../../../../../api/types';
import { useLanguage } from '../../../../../../components/contexts/LanguageContextManager';
import Form from '../../../../../../components/fields/Form';
import { useThemeComponents } from '../../../../../../themes/hooks';
import { prepareKYCFieldPayload } from '../../../../../../utilities/kycPresets';
import useKYCFormValidator from '../../../../../../utilities/kycPresets/useKYCValidators';
import useHandleError from '../../../../../../utilities/useHandleError';
import useValidator from '../../../../../../utilities/useValidator';
import validators from '../../../../../../utilities/validators';
import useAgreementsValidator from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValidator';
import CustomerDetails from '../../../../../shared/JourneyPage/CustomerDetails';
import ConsentsAndDeclarations from '../../../../EventApplicationEntrypoint/ApplicantForm/ConsentsAndDeclarations';
import { useLeadDetailsContext } from '../../../LeadDetails/LeadDetailsContext';
import { extractSalesOfferFromLaunchpadLead } from '../shared';
import { ModalValues } from './Modal';

const StyledContainer = styled.div`
    margin-bottom: 20px;
`;
type ModalFormProps = {
    initialValues: ModalValues;
    setOpen: Dispatch<SetStateAction<boolean>>;
    selectedKYC: KycFieldSpecsFragment[];
    selectedConsents: ApplicationAgreementDataFragment[];
    featureKindSelected: Array<SalesOfferFeatureKind>;
    setIsSubmitting: (v: boolean) => void;
};

const ModalForm = ({
    initialValues,
    setOpen,
    selectedKYC,
    selectedConsents,
    featureKindSelected,
    setIsSubmitting,
}: ModalFormProps) => {
    const { module, finance, id } = extractSalesOfferFromLaunchpadLead();
    const { t } = useTranslation(['launchpadSalesOfferDetails']);
    const { notification } = useThemeComponents();

    const { currentLanguageId } = useLanguage();
    const { endpoint } = useLeadDetailsContext();
    const [mutation] = useSendSalesOfferMutation();

    const onSubmit = useHandleError<ModalValues>(
        async values => {
            try {
                notification.loading({
                    content: t('launchpadSalesOfferDetails:messages.sendingPreOffer'),
                    duration: 0,
                    key: 'primary',
                });
                setIsSubmitting(true);

                const { data } = await mutation({
                    variables: {
                        id,
                        endpointId: endpoint.id,
                        featureKinds: featureKindSelected,
                        consent: Object.entries(values.agreements)
                            .map(([consentId, agreementFields]) => {
                                if (agreementFields.isAgreed) {
                                    if (agreementFields.platformsAgreed) {
                                        return {
                                            id: consentId,
                                            platformsAgreed: agreementFields.platformsAgreed,
                                        };
                                    }

                                    return { id: consentId };
                                }

                                return null;
                            })
                            .filter(Boolean),
                        languageId: currentLanguageId,
                        customer: prepareKYCFieldPayload(values.customer.fields),
                    },
                });

                notification.destroy('primary');

                if (data) {
                    notification.success({
                        content: t('launchpadSalesOfferDetails:messages.sentPreOfferSuccessful'),
                        key: 'secondary',
                    });
                    setOpen(false);
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            } finally {
                setIsSubmitting(false);
            }
        },
        [currentLanguageId, endpoint.id, featureKindSelected, id, mutation, notification, setOpen, t, setIsSubmitting]
    );

    const [, setPrefill] = useState<boolean>(false);

    const applicantsValidator = useKYCFormValidator({
        field: selectedKYC,
        extraSettings: null,
        moduleCountryCode: module.company.countryCode,
        prefix: 'customer.fields',
    });

    const agreementsValidator = useAgreementsValidator(selectedConsents, 'agreements');

    const validations = useMemo(
        () => validators.compose(applicantsValidator, agreementsValidator),
        [agreementsValidator, applicantsValidator]
    );
    const validate = useValidator(validations);

    const bank = module.banks.find(
        bank =>
            bank.__typename === 'SystemBank' &&
            bank.financeProducts.find(fp => fp.id === finance?.finance?.financeProductId)
    );

    return (
        <Formik initialValues={initialValues} onSubmit={onSubmit} validate={validate} enableReinitialize>
            {({ handleSubmit, values }) => (
                <Form id="kycModalForm" name="kycModalForm" onSubmitCapture={handleSubmit}>
                    <StyledContainer>
                        <CustomerDetails
                            customerKind={CustomerKind.Local}
                            hasGuarantorPreset={false}
                            hasUploadDocuments={bank?.__typename === 'SystemBank' && bank.hasUploadDocuments}
                            hasVSOUpload={bank?.__typename === 'SystemBank' && bank.hasUploadDocuments}
                            kycExtraSettings={null}
                            kycPresets={selectedKYC}
                            removeDocument={null}
                            setPrefill={setPrefill}
                            showRemarks={bank?.__typename === 'SystemBank' && bank.showCommentsField}
                            uploadDocument={null}
                            withFinancing
                        />
                    </StyledContainer>

                    <ConsentsAndDeclarations applicationAgreements={selectedConsents} hideDivider />
                </Form>
            )}
        </Formik>
    );
};

export default ModalForm;
