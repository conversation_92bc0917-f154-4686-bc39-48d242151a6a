import { Dispatch, SetStateAction, useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SalesOfferKycPresetSpecsFragment } from '../../../../../../api';
import { SalesOfferConsentsSpecsFragment } from '../../../../../../api/fragments/SalesOfferConsentsSpecs';
import { SalesOfferFeatureKind } from '../../../../../../api/types';
import { useThemeComponents } from '../../../../../../themes/hooks';
import { getInitialValues, KYCPresetFormFields } from '../../../../../../utilities/kycPresets';
import { getApplicantAgreements } from '../../../../../shared/CIPage/ConsentAndDeclarations/getAgreements';
import useAgreementsValues, {
    AgreementValues,
} from '../../../../../shared/CIPage/ConsentAndDeclarations/useAgreementsValues';
import { getApplicantKyc } from '../../../../StandardApplicationEntrypoint/KYCPage/getKyc';
import { extractSalesOfferFromLaunchpadLead, retrieveLaunchpadLead } from '../shared';
import ModalForm from './ModalForm';

export type ModalProps = {
    open: boolean;
    setOpen: Dispatch<SetStateAction<boolean>>;
    featureKindSelected: Array<SalesOfferFeatureKind>;
};

const retrieveKYCConsentsBasedOnSelectedFeatureKind = (
    featureKindSelected: Array<SalesOfferFeatureKind>,
    consents: SalesOfferConsentsSpecsFragment,
    kycPresets: SalesOfferKycPresetSpecsFragment
) => {
    const keysToPick = featureKindSelected
        .map(featureKind => {
            switch (featureKind) {
                case SalesOfferFeatureKind.MainDetails:
                    return 'coe';

                case SalesOfferFeatureKind.Vehicle:
                    return 'specification';

                case SalesOfferFeatureKind.Finance:
                    return 'finance';

                case SalesOfferFeatureKind.Insurance:
                    return 'insurance';

                case SalesOfferFeatureKind.Vsa:
                    return 'salesOffer';

                default:
                    return null;
            }
        })
        .filter(Boolean);

    return useMemo(
        () => ({
            consents: [
                ...new Map(
                    keysToPick
                        .flatMap(key => getApplicantAgreements(consents[key]))
                        .map(consent => [consent.id, consent])
                ).values(),
            ],
            kyc: [
                ...new Map(
                    keysToPick.flatMap(key => getApplicantKyc(kycPresets[key]) ?? []).map(kyc => [kyc.key, kyc])
                ).values(),
            ],
        }),
        [consents, kycPresets, keysToPick]
    );
};

export type ModalValues = {
    customer: {
        fields: KYCPresetFormFields;
    };
    agreements: AgreementValues;
    featureKinds: Array<SalesOfferFeatureKind>;
};

const Modal = ({ open, setOpen, featureKindSelected }: ModalProps) => {
    const { t } = useTranslation(['launchpadSalesOfferDetails']);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const { Modal, Button } = useThemeComponents();
    const { customer } = retrieveLaunchpadLead();
    const { consents, kycPresets } = extractSalesOfferFromLaunchpadLead();

    const handleClose = useCallback(() => {
        setOpen(false);
    }, [setOpen]);

    const { consents: selectedConsents, kyc: selectedKYC } = retrieveKYCConsentsBasedOnSelectedFeatureKind(
        featureKindSelected,
        consents,
        kycPresets
    );

    const agreements = useAgreementsValues(selectedConsents);
    const initialValues: ModalValues = useMemo(
        () => ({
            agreements,
            customer: {
                fields: {
                    ...getInitialValues(customer.fields, selectedKYC),
                },
            },
            featureKinds: featureKindSelected,
        }),
        [customer.fields, featureKindSelected, selectedKYC, agreements]
    );

    const footerButton = useMemo(
        () => [
            <Button
                key="submit"
                disabled={isSubmitting}
                form="kycModalForm"
                htmlType="submit"
                loading={isSubmitting}
                type="primary"
                block
            >
                {t('launchpadLeadDetails:kyc.modal.buttons.submit')}
            </Button>,
            <Button key="cancel" disabled={isSubmitting} onClick={handleClose} type="tertiary" block>
                {t('launchpadLeadDetails:kyc.modal.buttons.cancel')}
            </Button>,
        ],
        [Button, handleClose, isSubmitting, t]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            footer={footerButton}
            onCancel={() => setOpen(false)}
            open={open}
            title={t('launchpadSalesOfferDetails:kyc.modal.title')}
            width={800}
            centered
            destroyOnClose
        >
            <ModalForm
                featureKindSelected={featureKindSelected}
                initialValues={initialValues}
                selectedConsents={selectedConsents}
                selectedKYC={selectedKYC}
                setIsSubmitting={setIsSubmitting}
                setOpen={setOpen}
            />
        </Modal>
    );
};

export default Modal;
