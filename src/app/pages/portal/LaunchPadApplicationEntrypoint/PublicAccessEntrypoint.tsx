import { Routes, Route } from 'react-router-dom';
// eslint-disable-next-line max-len
import { LaunchPadApplicationEntrypointContextDataFragment } from '../../../api/fragments/LaunchPadApplicationEntrypointContextData';
import JourneyPage from './remoteSalesOfferJourney/JourneyPage';
import OtpValidationPage from './remoteSalesOfferJourney/OtpValidationPage';
import ThankYouPage from './remoteSalesOfferJourney/ThankYouPage';

export type PublicAccessEntrypointProps = {
    endpoint: LaunchPadApplicationEntrypointContextDataFragment;
};

const PublicAccessEntrypoint = ({ endpoint }: PublicAccessEntrypointProps) => (
    <Routes>
        <Route key="authorize" element={<OtpValidationPage />} path="salesOffer/authorize" />
        <Route key="apply" element={<JourneyPage endpoint={endpoint} />} path="salesOffer/apply" />
        <Route key="thankyou" element={<ThankYouPage />} path="salesOffer/thankyou" />
    </Routes>
);

export default PublicAccessEntrypoint;
