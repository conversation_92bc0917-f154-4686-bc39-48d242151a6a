import { useApolloClient } from '@apollo/client';
import { PHeading } from '@porsche-design-system/components-react';
import { TFunction } from 'i18next';
import { isNil } from 'lodash/fp';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router';
import styled from 'styled-components';
import { SalesOfferFeatureKind } from '../../../../../api';
import {
    GenerateRemoteJourneyPasscodeDocument,
    GenerateRemoteJourneyPasscodeMutation,
    GenerateRemoteJourneyPasscodeMutationVariables,
} from '../../../../../api/mutations/generateRemoteJourneyPasscode';
import {
    GenerateSalesOfferJourneyTokenDocument,
    GenerateSalesOfferJourneyTokenMutation,
    GenerateSalesOfferJourneyTokenMutationVariables,
} from '../../../../../api/mutations/generateSalesOfferJourneyToken';
import {
    ValidateSalesOfferRemoteJourneyPasscodeDocument,
    ValidateSalesOfferRemoteJourneyPasscodeMutation,
    ValidateSalesOfferRemoteJourneyPasscodeMutationVariables,
} from '../../../../../api/mutations/validateSalesOfferRemoteJourneyPasscode';
import PortalLoadingElement from '../../../../../components/PortalLoadingElement';
import { useThemeComponents } from '../../../../../themes/hooks';
import useHandleError from '../../../../../utilities/useHandleError';
import NotFoundPage from '../../../NotFoundPage';
import PasscodeForm, { PasscodeFormValues } from './PasscodeForm';

const Layout = styled.div`
    display: flex;
    flex-direction: column;
    gap: 76px;
    align-items: center;
`;

const Container = styled.div`
    background: white;
    padding: 32px;
    border-radius: 12px;
    margin: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 36px;
`;

const useTitle = (featureKinds: SalesOfferFeatureKind[], t: TFunction) => {
    if (featureKinds.length > 1) {
        return t('salesOfferJourney:otp.title.preOffer');
    }

    switch (featureKinds[0]) {
        case SalesOfferFeatureKind.Vsa:
            return t('salesOfferJourney:otp.title.vsa');

        case SalesOfferFeatureKind.MainDetails:
            return t('salesOfferJourney:otp.title.mainDetails');

        case SalesOfferFeatureKind.Vehicle:
            return t('salesOfferJourney:otp.title.vehicle');

        case SalesOfferFeatureKind.Finance:
            return t('salesOfferJourney:otp.title.financing');

        case SalesOfferFeatureKind.Insurance:
            return t('salesOfferJourney:otp.title.insurance');

        default:
            return '';
    }
};
const OtpValidationPage = () => {
    const state = useLocation().state as { secret: string; featureKinds: SalesOfferFeatureKind[] };
    const { secret, featureKinds } = state;

    const apolloClient = useApolloClient();
    const { t } = useTranslation(['salesOfferJourney', 'validationPage']);
    const { notification } = useThemeComponents();
    const navigate = useNavigate();

    const navToJourney = useCallback(
        (token: string) => {
            navigate('../apply', {
                state: {
                    token,
                },
            });
        },
        [navigate]
    );

    const submitPasscode = useHandleError(
        async ({ code }: PasscodeFormValues) => {
            notification.loading({
                content: t('validationPage:messages.submitting'),
                duration: 0,
                key: 'primary',
            });

            const { data } = await apolloClient
                .mutate<
                    ValidateSalesOfferRemoteJourneyPasscodeMutation,
                    ValidateSalesOfferRemoteJourneyPasscodeMutationVariables
                >({
                    mutation: ValidateSalesOfferRemoteJourneyPasscodeDocument,
                    variables: { secret, code },
                })
                .finally(() => {
                    notification.destroy('primary');
                });

            navToJourney(data.result);
        },
        [notification, t, apolloClient, secret, navToJourney]
    );

    const getToken = useCallback(
        () =>
            apolloClient
                .mutate<GenerateSalesOfferJourneyTokenMutation, GenerateSalesOfferJourneyTokenMutationVariables>({
                    mutation: GenerateSalesOfferJourneyTokenDocument,
                    variables: { secret },
                })
                .then(response => {
                    const { data } = response;

                    if (!data?.result) {
                        return Promise.resolve(null);
                    }

                    return Promise.resolve(data?.result);
                }),
        [apolloClient, secret]
    );

    const resendOTP = useCallback(
        () =>
            apolloClient
                .mutate<GenerateRemoteJourneyPasscodeMutation, GenerateRemoteJourneyPasscodeMutationVariables>({
                    mutation: GenerateRemoteJourneyPasscodeDocument,
                    variables: { secret },
                })
                .then(response => {
                    const { data } = response;

                    if (!data?.result) {
                        return Promise.resolve(null);
                    }

                    const limitTime = new Date(data.result.expiredAt).getTime();
                    const limitTimeInSeconds = Math.floor((limitTime - new Date().getTime()) / 1000);
                    setCountdown(limitTimeInSeconds);

                    return Promise.resolve(response);
                }),
        [apolloClient, secret]
    );

    const [countdown, setCountdown] = useState<number | null>(null);

    useEffect(() => {
        // if this flow only include vehicle signing
        // then we do not need opt
        if (featureKinds.length === 1 && featureKinds[0] === SalesOfferFeatureKind.Vehicle) {
            getToken().then(token => {
                if (token) {
                    navToJourney(token);
                }
            });

            return;
        }

        // initialize the OTP
        resendOTP();
    }, [resendOTP, featureKinds, getToken, navToJourney]);

    const title = useTitle(featureKinds, t);

    if (!secret || featureKinds?.length < 0) {
        return <NotFoundPage />;
    }

    // pending for initial OTP
    if (isNil(countdown)) {
        return <PortalLoadingElement />;
    }

    return (
        <Layout>
            <PHeading size="xx-large" tag="h2">
                {title}
            </PHeading>
            <Container>
                <PHeading size="large" tag="h3">
                    {t('salesOfferJourney:otp.notification')}
                </PHeading>
                <PasscodeForm handleSubmit={submitPasscode} resendCode={resendOTP} resendCountdown={countdown} />
            </Container>
        </Layout>
    );
};

export default OtpValidationPage;
