import { PButtonPure } from '@porsche-design-system/components-react';
import { Form, Formik, FormikHelpers, useField } from 'formik';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useThemeComponents } from '../../../../../themes/hooks';

export type PasscodeFormValues = { code: string };

export type PasscodeFormProps = {
    resendCode: () => void;
    resendCountdown: number;
    handleSubmit: (values: PasscodeFormValues, helpers: FormikHelpers<PasscodeFormValues>) => Promise<void>;
};

const initialValues = { code: '' };

const OTPInputField = ({ name }: { name: string }) => {
    const [field, meta, helper] = useField({ name });
    const { OTPInput } = useThemeComponents();
    const inputRefs = useRef<Array<any>>([]);

    const onChange = useCallback(
        (value: string) => {
            helper.setValue(value);
        },
        [helper]
    );

    return <OTPInput error={meta?.error} inputRefs={inputRefs} onChange={onChange} values={field.value || ''} />;
};

const PasscodeForm = ({ handleSubmit, resendCode, resendCountdown }: PasscodeFormProps) => {
    const { t } = useTranslation('validationPage');
    const { Button } = useThemeComponents();
    const [count, setCount] = useState(resendCountdown);

    useEffect(() => {
        setCount(resendCountdown);
    }, [resendCountdown]);

    useEffect(() => {
        const interval = setInterval(() => {
            if (count !== 0) {
                setCount(prevState => prevState - 1);
            }
        }, 1000);

        return () => {
            clearInterval(interval);
        };
    }, [count, setCount]);

    const linkElement = useMemo(
        () => <PButtonPure disabled={count !== 0} icon="none" onClick={resendCode} type="button" underline />,
        [count, resendCode]
    );

    return (
        <Formik initialValues={initialValues} onSubmit={handleSubmit}>
            {({ isSubmitting, values }) => (
                <Form style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: 36 }}>
                    <OTPInputField name="code" />
                    <div
                        style={{
                            width: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            gap: 16,
                        }}
                    >
                        <Button
                            disabled={!values.code}
                            htmlType="submit"
                            loading={isSubmitting}
                            size="large"
                            type="primary"
                            block
                        >
                            {t('validationPage:verifyBtn')}
                        </Button>
                        <div
                            style={{
                                width: '100%',
                                textAlign: 'center',
                            }}
                        >
                            <Trans count={count} i18nKey="validationPage:resendOTP">
                                {linkElement}
                            </Trans>
                        </div>
                    </div>
                </Form>
            )}
        </Formik>
    );
};

export default PasscodeForm;
