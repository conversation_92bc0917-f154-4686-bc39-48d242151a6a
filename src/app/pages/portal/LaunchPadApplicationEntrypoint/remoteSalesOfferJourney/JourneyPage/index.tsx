import { useLocation } from 'react-router-dom';
// eslint-disable-next-line max-len
import { LaunchPadApplicationEntrypointContextDataFragment } from '../../../../../api/fragments/LaunchPadApplicationEntrypointContextData';
import { Maybe } from '../../../../../api/types';
import NotFoundResult from '../../../../../components/results/NotFoundResult';

export type JourneyLocationState = {
    token: string;
};

export type JourneyProps = {
    endpoint: LaunchPadApplicationEntrypointContextDataFragment;
};

const JourneyPage = ({ endpoint }: JourneyProps) => {
    const state = useLocation().state as Maybe<JourneyLocationState>;

    if (!state) {
        return <NotFoundResult />;
    }

    const { token } = state;

    if (!token) {
        // that shouldn't happen as well
        return <NotFoundResult />;
    }

    return 'Sales Offer Journey';
};

export default JourneyPage;
