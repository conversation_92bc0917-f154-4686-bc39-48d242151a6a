import { Drawer } from 'antd';
import styled from 'styled-components';
import breakpoints from '../../../../utilities/breakpoints';

export const StyledDrawer = styled(Drawer)<{ placement: 'top' | 'right' | 'bottom' | 'left' }>`
    .ant-drawer-content-wrapper {
        @media screen and (max-width: ${breakpoints.md}) {
            min-width: 98vw;
        }
        width: 330px;
    }
    .ant-drawer-left > .ant-drawer-content-wrapper {
        box-shadow: 0px;
    }
    .ant-drawer-right > .ant-drawer-content-wrapper {
        box-shadow: 0px;
    }

    .ant-drawer-body {
        background-color: #eeeff2;
        padding: 24px 0px;

        border-left: ${({ placement }) => (placement === 'right' ? '1px solid #D8D8DB' : 'none')};
        border-right: ${({ placement }) => (placement === 'left' ? '1px solid #D8D8DB' : 'none')};
    }
`;

export const DrawerHeader = styled.div<{ $rightAligned: boolean }>`
    width: 100%;
    padding: 0 24px 2rem 24px;
    display: flex;
    justify-content: space-between;
    flex-direction: ${({ $rightAligned }) => ($rightAligned ? 'row' : 'row-reverse')};
    align-items: center;
`;

export const FilterContainer = styled.div<{ $leftBordered: boolean; $rightBordered: boolean }>`
    width: 100%;
    padding: 0 24px;

    display: flex;
    flex-direction: column;
    margin-top: -8px;

    @media screen and (min-width: ${breakpoints.md}) {
        padding-left: 24px;
        padding-right: 24px;
    }
`;

export const FilterGroupContainer = styled.div`
    background: transparent;
    width: 100%;
    margin: 0;
    padding: 0;
    margin-top: 20px;
    margin-bottom: 20px;

    &:empty {
        margin: 0;
        padding: 0;
    }

    & p-checkbox-wrapper {
        margin-bottom: 0.5rem;
    }
`;

export const ResetButtonContainer = styled.div`
    display: flex;
    justify-content: center;
    width: 100%;
`;

export const ResetButton = styled.p`
    text-decoration: underline;
    font-size: 1rem;
    :hover {
        cursor: pointer;
        background-color: transparent;
    }
`;
