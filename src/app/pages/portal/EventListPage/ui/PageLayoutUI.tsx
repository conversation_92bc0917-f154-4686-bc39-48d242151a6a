import { Row, Typography } from 'antd';
import styled, { css } from 'styled-components';
import { LayoutType } from '../../../../api/types';
import { useRouter } from '../../../../components/contexts/shared';
import breakpoints from '../../../../utilities/breakpoints';

export const EventListPageWrapper = styled.div`
    ${props =>
        props.theme.layoutType === LayoutType.PorscheV3
            ? css`
                  .ant-pro-page-container-warp > .ant-page-header {
                      padding: 36px 0px 16px;
                  }
              `
            : css`
                  .ant-page-header .ant-page-header-heading-title > .ant-typography {
                      font-size: 28px;
                  }
                  .ant-pro-page-container-children-content {
                      background-color: rgb(238, 239, 242);
                      margin: 0;
                      padding: 24px 24px 36px;
                  }
              `}
`;

export const HeaderButtonContainer = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;

    // V2 style
    & .ant-btn.ant-btn-link {
        padding: 0;
        height: 2rem;
        font-size: var(--button-font-size, 1rem);
        margin-left: 20px;

        > span.anticon-mail {
            height: var(--button-font-size);
        }

        > svg {
            fill: currentColor;
        }

        > svg + span {
            margin-left: 8px;
            margin-top: 0;
        }
    }

    // V3 style
    & .p-button {
        margin-left: 20px;
    }
`;

export const EventListCount = styled(Typography.Text)`
    margin: ${props => (props.theme.layoutType === LayoutType.PorscheV3 ? '0' : '52px')};
    color: #6b6d70;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
`;

export const InnerContainer = styled.div<{ isFilterOpened: boolean }>`
    min-height: 72vh;

    @media screen and (min-width: ${breakpoints.md}) {
        width: ${({ isFilterOpened }) => (isFilterOpened ? 'calc(100% - 380px)' : '100%')};
    }
`;

export const CardListContainer = styled(Row)`
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: 0;
    margin: 36px 0px 0px;

    @media screen and (min-width: ${breakpoints.md}) {
        flex-direction: row;
        justify-content: flex-start;
        margin: ${props => (props.theme.layoutType === LayoutType.PorscheV3 ? '36px 0px 0px' : '36px 36px 0px')};
    }
`;

export const EventListTitle = ({ title }: { title: string }) => {
    const { layout } = useRouter();

    return layout.__typename !== 'PorscheV3Layout' ? <Typography.Title level={4}>{title}</Typography.Title> : title;
};

export const PaginationContainer = styled.div`
    margin: ${props => (props.theme.layoutType === LayoutType.PorscheV3 ? '16px 0' : '16px 16px')};
    display: flex;
    flex-wrap: wrap;
    row-gap: 8px;
    justify-content: flex-end;
`;
