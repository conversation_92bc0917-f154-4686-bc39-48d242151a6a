import { Card } from 'antd';
import styled from 'styled-components';
import breakpoints from '../../../../utilities/breakpoints';

export const StyledCard = styled(Card)`
    border-radius: 12px;
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100%;

    .ant-card-cover {
        overflow: hidden;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        margin: 0;

        img {
            height: auto;
            width: 100%;
            aspect-ratio: 16/9;
            object-fit: cover;
            object-position: center center;
        }
    }

    .ant-card-body {
        padding: 30px 24px;

        @media screen and (max-width: ${breakpoints.md}) {
            padding: 29px 24px 22px;
        }
    }

    .ant-card-meta-title {
        font-size: 28px;
        line-height: 35px;
        color: #000;
        margin-bottom: 14px;

        @media screen and (max-width: ${breakpoints.md}) {
            font-size: 20px;
            margin-bottom: 13px;
            line-height: 24px;
        }
    }

    .ant-card-meta-detail > div:not(:last-child) {
        margin-bottom: 14px;

        @media screen and (max-width: ${breakpoints.md}) {
            margin-bottom: 13px;
        }
    }

    .ant-card-meta-description {
        font-size: 16px;
        line-height: 19px;
        font-weight: 900;
        color: #000000;
    }

    .ant-card-actions {
        margin-top: auto;
        padding-top: 200px;
        border-top: 0;
        padding: 0 24px;
        border-bottom-right-radius: 8px;
        border-bottom-left-radius: 8px;
        & > li {
            margin-top: 2px;
            margin-bottom: 24px;

            .ant-space {
                display: flex;
                width: 100%;

                .ant-space-item {
                    width: 50%;
                }
            }
        }
    }
`;

export const CardDescriptionContainer = styled.div`
    min-height: 14rem;
`;

export const CardTitleContainer = styled.div`
    margin-bottom: 16px;
`;

export const CardActionButtonContainer = styled.div`
    display: flex;
    gap: 10px;
    flex-wrap: wrap;

    & > * {
        flex: 1 1 150px;
    }

    & .ant-btn {
        min-width: 100%;
    }

    & div:has(> p-button) {
        min-width: 100%;

        & > p-button {
            width: 100%;
        }
    }
`;

export const TagContainer = styled.div`
    display: table;
    margin-top: 10px;
    margin-bottom: 16px;
`;
