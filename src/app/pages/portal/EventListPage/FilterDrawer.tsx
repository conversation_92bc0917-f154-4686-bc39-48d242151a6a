import { Form as AntdForm } from 'antd';
import dayjs from 'dayjs';
import { Formik, useFormikContext } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetPublicAssigneesQuery } from '../../../api/queries/getPublicAssignees';
import { type PeriodPayload } from '../../../api/types';
import { useCompany } from '../../../components/contexts/CompanyContextManager';
import Form from '../../../components/fields/Form';
import { defaultFilterOption } from '../../../components/fields/SelectField';
import { useThemeComponents } from '../../../themes/hooks';
import useAccessFilter from '../../../utilities/useAccessFilter';
import useEnableFilter from '../../../utilities/useEnableFilter';
import { Action, State } from '../../shared/EventList/useEventReducer';
import FilterGroup from './FilterGroup';
import {
    <PERSON>erHeader,
    FilterContainer,
    FilterGroupContainer,
    ResetButton,
    ResetButtonContainer,
    StyledDrawer,
} from './ui';

type FilterFields = {
    eventId: string;
    eventName: string;
    startDateEndDate: PeriodPayload;
    assignee: string;
    formAccessType: boolean[];
    payment: boolean[];
};

const FilterDrawerFields = ({ dispatch }: { dispatch: (value: Action) => void }) => {
    const { values, setFieldValue } = useFormikContext<FilterFields>();
    const { t } = useTranslation(['core', 'eventList']);
    // get company context
    const company = useCompany(true);

    const {
        FormFields: { RangePickerField, SelectField },
        Search,
        Typography,
    } = useThemeComponents();

    const { Item: FormItem } = AntdForm;
    const { Text } = Typography;

    const direction = t('core:orientation');

    const defaultAccessFilter = useAccessFilter();
    const defaultPaymentFilter = useEnableFilter();

    const { data, loading } = useGetPublicAssigneesQuery({
        fetchPolicy: 'cache-and-network',
        variables: { companyId: company?.id },
    });

    const assigneeOptions = useMemo(() => {
        if (!data?.result?.length) {
            return [];
        }

        const mappedResult = data?.result.map(({ id, displayName }) => ({ label: displayName, value: id }));

        return [{ label: t('eventList:filter.options.selectAll'), value: null }, ...mappedResult];
    }, [data?.result, t]);

    const searchEventId = useCallback(
        (value: string) => {
            dispatch({
                type: 'setPartialFilter',
                filterBy: { identifier: value },
            });
        },
        [dispatch]
    );
    const searchEventName = useCallback(
        (value: string) => {
            dispatch({
                type: 'setPartialFilter',
                filterBy: { displayName: value },
            });
        },
        [dispatch]
    );
    const selectDateRange = useCallback(
        (dates: [dayjs.Dayjs, dayjs.Dayjs]) => {
            dispatch({
                type: 'setPartialFilter',
                filterBy: {
                    startDateEndDate:
                        dates[0].isValid() && dates[1].isValid()
                            ? {
                                  start: dates[0].tz(company?.timeZone).startOf('date').toDate(),
                                  end: dates[1].tz(company?.timeZone).endOf('date').toDate(),
                              }
                            : null,
                },
            });
        },
        [company?.timeZone, dispatch]
    );
    const selectAssignee = useCallback(
        (value: string) => {
            dispatch({
                type: 'setPartialFilter',
                filterBy: {
                    assigned: value ? assigneeOptions.find(assignee => assignee.value === value)?.label : null,
                },
            });
        },
        [assigneeOptions, dispatch]
    );
    const selectFormAccessType = useCallback(
        (value: boolean[]) => {
            setFieldValue('formAccessType', value);
            dispatch({
                type: 'setPartialFilter',
                filterBy: { privateAccesses: value },
            });
        },
        [dispatch, setFieldValue]
    );

    const selectPayment = useCallback(
        (value: boolean[]) => {
            setFieldValue('payment', value);
            dispatch({
                type: 'setPartialFilter',
                filterBy: { payment: value },
            });
        },
        [dispatch, setFieldValue]
    );

    const { accessFilter, paymentFilter } = useMemo(
        () => ({
            accessFilter: defaultAccessFilter.map(({ text, value }) => ({ label: text, value })),
            paymentFilter: defaultPaymentFilter.map(({ text, value }) => ({ label: text, value })),
        }),
        [defaultAccessFilter, defaultPaymentFilter]
    );

    return (
        <Form id="event-list-filter" name="event-list-filter">
            <FilterContainer $leftBordered={direction !== 'rtl'} $rightBordered={direction === 'rtl'}>
                <FormItem name="eventId">
                    <Text level={4}>{t('eventList:filter.fields.eventId')}</Text>
                    <Search
                        onChange={event => setFieldValue('eventId', event.target.value)}
                        onSearch={searchEventId}
                        value={values.eventId}
                        enterButton
                    />
                </FormItem>
                <FormItem name="eventName">
                    <Text level={4}>{t('eventList:filter.fields.eventName')}</Text>
                    <Search
                        onChange={event => setFieldValue('eventName', event.target.value)}
                        onSearch={searchEventName}
                        value={values.eventName}
                        enterButton
                    />
                </FormItem>
                <RangePickerField
                    label={t('eventList:filter.fields.startDateEndDate')}
                    name="startDateEndDate"
                    onChange={selectDateRange}
                    picker="date"
                />
                <SelectField
                    disabled={loading || !assigneeOptions.length}
                    filterOption={defaultFilterOption}
                    label={t('eventList:filter.fields.assignee')}
                    name="assignee"
                    onChange={selectAssignee}
                    options={assigneeOptions}
                    allowClear
                    showSearch
                />
                <FilterGroupContainer>
                    <FilterGroup
                        onChange={selectFormAccessType}
                        options={accessFilter}
                        title={t('eventList:filter.fields.formAccessType')}
                        value={values.formAccessType}
                    />
                </FilterGroupContainer>
                <FilterGroupContainer>
                    <FilterGroup
                        onChange={selectPayment}
                        options={paymentFilter}
                        title={t('eventList:filter.fields.payment')}
                        value={values.payment}
                    />
                </FilterGroupContainer>
            </FilterContainer>
        </Form>
    );
};

const FilterDrawer = ({
    openFilterDrawer,
    setOpenFilterDrawer,
    state,
    dispatch,
}: {
    openFilterDrawer: boolean;
    setOpenFilterDrawer: (value: boolean) => void;
    state: State;
    dispatch: (value: Action) => void;
}) => {
    const { t } = useTranslation(['core', 'eventList']);
    const { Button, Typography } = useThemeComponents();
    const { Heading } = Typography;

    const initialValues: FilterFields = useMemo(
        () => ({
            eventId: state.filter.identifier,
            eventName: state.filter.displayName,
            startDateEndDate: {
                start: state.filter.startDateEndDate?.start,
                end: state.filter.startDateEndDate?.end,
            },
            assignee: state.filter.assigned,
            formAccessType: state.filter.privateAccesses || [],
            payment: state.filter.payment || [],
        }),
        [state.filter]
    );

    const resetFilter = useCallback(() => {
        dispatch({
            type: 'setPartialFilter',
            filterBy: {
                identifier: null,
                displayName: null,
                startDateEndDate: null,
                assigned: null,
                privateAccesses: [],
                payment: [],
            },
        });
    }, [dispatch]);

    return (
        <StyledDrawer
            closable={false}
            mask={false}
            open={openFilterDrawer}
            placement={t('core:orientation') === 'rtl' ? 'left' : 'right'}
            push
        >
            {openFilterDrawer && (
                <>
                    <DrawerHeader $rightAligned={t('core:orientation') === 'ltr'}>
                        <Heading level={5}>{t('eventList:filter.title')}</Heading>
                        <Button
                            onClick={() => setOpenFilterDrawer(false)}
                            porscheFallbackIcon="close"
                            type="ghost"
                            isCompact
                        />
                    </DrawerHeader>
                    <Formik
                        key="event-list-filter"
                        initialValues={initialValues}
                        onReset={resetFilter}
                        onSubmit={null}
                        validate={null}
                    >
                        {({ resetForm }) => (
                            <>
                                <FilterDrawerFields dispatch={dispatch} />
                                <ResetButtonContainer>
                                    <ResetButton onClick={() => resetForm()}>{t('eventList:filter.reset')}</ResetButton>
                                </ResetButtonContainer>
                            </>
                        )}
                    </Formik>
                </>
            )}
        </StyledDrawer>
    );
};

export default FilterDrawer;
