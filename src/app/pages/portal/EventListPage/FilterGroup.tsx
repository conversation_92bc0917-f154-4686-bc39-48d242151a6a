import { Typography } from 'antd';
import { CheckboxOptionType as AntdCheckboxOptionType } from 'antd/lib/checkbox/Group';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { useThemeComponents } from '../../../themes/hooks';

type CheckboxOptionType = Omit<AntdCheckboxOptionType, 'value'> & { value: boolean };

type FilterGroupProps = {
    title: string;
    options: CheckboxOptionType[];
    value: boolean[];
    onChange: (value: boolean[]) => void;
};

const FilterGroupContainer = styled.div`
    display: flex;
    flex-direction: column;
    width: 100%;
`;

const FilterGroup = ({ title, options, value, onChange }: FilterGroupProps) => {
    const { t } = useTranslation(['eventList']);
    const { Checkbox, CheckboxGroup } = useThemeComponents();

    return (
        <FilterGroupContainer>
            <Typography.Title level={5} style={{ width: '100%' }}>
                {title}
            </Typography.Title>
            {options.length > 1 && (
                <Checkbox
                    checked={value.length === options.length}
                    indeterminate={value.length > 0 && value.length < options.length}
                    onChange={() =>
                        onChange(value.length === options.length ? [] : options.map(option => option.value))
                    }
                    style={{ width: '100%' }}
                    value="all"
                >
                    {t('eventList:filter.options.selectAll')}
                </Checkbox>
            )}
            <CheckboxGroup
                onChange={onChange}
                options={options}
                style={{ width: '100%', display: 'flex', flexDirection: 'column' }}
                value={value}
            />
        </FilterGroupContainer>
    );
};

export default memo(FilterGroup);
