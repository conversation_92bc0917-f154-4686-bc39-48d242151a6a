import { PTag } from '@porsche-design-system/components-react';
import { Tag } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../components/contexts/shared';
import { useThemeComponents } from '../../../themes/hooks';
import { getDateTimeOffsetFormat } from '../../../utilities/date';
import { EventListingType } from './types';
import { CardDescriptionContainer, CardTitleContainer, TagContainer } from './ui';

const getPorscheTagColor = (color: string) => {
    switch (color) {
        case 'blue':
            return 'notification-info-soft';

        case 'green':
            return 'notification-success-soft';

        default:
            return 'background-base';
    }
};

const CardDescriptions = ({ event }: { event: EventListingType }) => {
    const { t } = useTranslation(['eventList', 'common']);
    const { layout } = useRouter();
    const { identifier } = event;
    const { Typography } = useThemeComponents();
    const { Text, Heading } = Typography;

    const tags = useMemo(() => {
        const items = [
            {
                text: t('eventList:card.content.tags.access', {
                    access: event.privateAccess ? t('eventList:data.login') : t('eventList:data.public'),
                }),
                color: 'blue',
            },
        ];

        if (event.hasPayment) {
            items.push({
                text: t('eventList:card.content.tags.payment', {
                    payment: event.privateAccess ? t('common:options.enable.enabled') : t('eventList:data.public'),
                }),
                color: 'green',
            });
        }

        return items;
    }, [event, t]);

    const assignee = useMemo(() => {
        if (event.privateAccess || !event.publicSalesPerson) {
            return null;
        }

        return event.publicSalesPerson.__typename === 'DealershipPublicSalesPerson'
            ? event.publicSalesPerson.defaultSalesPerson.displayName
            : null;
    }, [event]);

    return (
        <>
            <CardTitleContainer>
                <Heading level={3}>{event.displayName}</Heading>
            </CardTitleContainer>
            <CardDescriptionContainer>
                <Text level={5}>
                    {t('eventList:card.content.identifier', {
                        identifier,
                    })}
                </Text>
                <TagContainer>
                    {tags.map(tag =>
                        layout.__typename === 'PorscheV3Layout' ? (
                            <PTag key={tag.text} color={getPorscheTagColor(tag.color)} theme="light">
                                {tag.text}
                            </PTag>
                        ) : (
                            <Tag key={tag.text} color={tag.color}>
                                {tag.text}
                            </Tag>
                        )
                    )}
                </TagContainer>
                <Heading level={5}>
                    {getDateTimeOffsetFormat(event.period.start, t, event.module.company.timeZone)}
                </Heading>
                <Text level={5}>{t('eventList:columns.startDate')}</Text>
                <Heading level={5}>
                    {getDateTimeOffsetFormat(event.period.end, t, event.module.company.timeZone)}
                </Heading>
                <Text level={5}>{t('eventList:columns.endDate')}</Text>
                {assignee && (
                    <>
                        <Heading level={5}>{assignee}</Heading>
                        <Text level={5}>{t('eventList:columns.assigned')}</Text>
                    </>
                )}
            </CardDescriptionContainer>
        </>
    );
};

export default CardDescriptions;
