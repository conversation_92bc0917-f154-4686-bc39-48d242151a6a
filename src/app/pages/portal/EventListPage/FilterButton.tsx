import { RightOutlined, LeftOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { useRouter } from '../../../components/contexts/shared';
import { useThemeComponents } from '../../../themes/hooks';

type FilterButtonProps = {
    openFilterDrawer: boolean;
    setOpenFilterDrawer: (value: boolean) => void;
};

const FilterButton = ({ openFilterDrawer, setOpenFilterDrawer }: FilterButtonProps) => {
    const { t } = useTranslation(['eventList']);
    const { Button } = useThemeComponents();
    const { layout } = useRouter();

    if (layout?.__typename === 'PorscheV3Layout') {
        return (
            <Button
                onClick={() => setOpenFilterDrawer(!openFilterDrawer)}
                porscheFallbackIcon="filter"
                type="secondary"
            >
                {t('eventList:actions.filter')}
            </Button>
        );
    }

    return (
        <Button
            className={openFilterDrawer ? 'ant-btn-icon-only' : ''}
            icon={openFilterDrawer ? <RightOutlined /> : <LeftOutlined />}
            onClick={() => setOpenFilterDrawer(!openFilterDrawer)}
            porscheFallbackIcon={openFilterDrawer ? 'arrow-head-right' : 'arrow-head-left'}
            type="tertiary"
        >
            {!openFilterDrawer && t('carList:filterButton')}
        </Button>
    );
};

export default FilterButton;
