import { validators } from '@amille/simple-validators';
import { TFunction } from 'i18next';
import validatorsModule from './validators';

// Mock translation function
const mockT: TFunction = (key: string, options?: any) => {
    if (key === 'common:formErrors.minLength') {
        return `Minimum ${options?.minLength} characters required`;
    }
    if (key === 'common:formErrors.maxLength') {
        return `Maximum ${options?.maxLength} characters allowed`;
    }
    return key;
};

// Mock context for validators
const createMockContext = (t?: TFunction) => ({
    defaultMessages: {
        requiredValue: 'This field is required.',
        noWhitespace: 'This field cannot be empty or whitespace only',
        invalidFormat: 'Invalid format',
        invalidNumber: 'Invalid number format',
    },
    outerContext: {
        t: t || mockT,
    },
});

describe('KYC Field Validators', () => {
    describe('validKYCName', () => {
        it('should accept valid names with international characters', () => {
            const validator = validatorsModule.validKYCName('name', true);
            const context = createMockContext();

            // Valid names
            expect(validator('John', {}, {}, context)).toBeNull();
            expect(validator('José María', {}, {}, context)).toBeNull();
            expect(validator("O'Connor", {}, {}, context)).toBeNull();
            expect(validator('Jean-Pierre', {}, {}, context)).toBeNull();
            expect(validator('李小明', {}, {}, context)).toBeNull();
            expect(validator('محمد', {}, {}, context)).toBeNull();
        });

        it('should reject whitespace-only inputs', () => {
            const validator = validatorsModule.validKYCName('name', true);
            const context = createMockContext();

            expect(validator('   ', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
            expect(validator('\t\n', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
            expect(validator('', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
        });

        it('should reject invalid characters', () => {
            const validator = validatorsModule.validKYCName('name', true);
            const context = createMockContext();

            expect(validator('John123', {}, {}, context)).toBe('Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed');
            expect(validator('John@Doe', {}, {}, context)).toBe('Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed');
        });

        it('should handle optional fields correctly', () => {
            const validator = validatorsModule.validKYCName('name', false);
            const context = createMockContext();

            // Optional field can be empty
            expect(validator('', {}, {}, context)).toBeNull();
            expect(validator(null, {}, {}, context)).toBeNull();
            expect(validator(undefined, {}, {}, context)).toBeNull();

            // But still rejects whitespace-only
            expect(validator('   ', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
        });
    });

    describe('validKYCAddress', () => {
        it('should accept valid addresses', () => {
            const validator = validatorsModule.validKYCAddress('address', true);
            const context = createMockContext();

            expect(validator('123 Main St', {}, {}, context)).toBeNull();
            expect(validator('Apt 4B, Building #5', {}, {}, context)).toBeNull();
            expect(validator('1234 Oak Ave.', {}, {}, context)).toBeNull();
        });

        it('should reject whitespace-only inputs', () => {
            const validator = validatorsModule.validKYCAddress('address', true);
            const context = createMockContext();

            expect(validator('   ', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
        });
    });

    describe('validKYCNumeric', () => {
        it('should accept valid numbers', () => {
            const validator = validatorsModule.validKYCNumeric('amount', true, 0, 1000);
            const context = createMockContext();

            expect(validator('100', {}, {}, context)).toBeNull();
            expect(validator('0', {}, {}, context)).toBeNull();
            expect(validator('1000', {}, {}, context)).toBeNull();
            expect(validator(500, {}, {}, context)).toBeNull();
        });

        it('should reject invalid numbers', () => {
            const validator = validatorsModule.validKYCNumeric('amount', true, 0, 1000);
            const context = createMockContext();

            expect(validator('abc', {}, {}, context)).toBe('Invalid number format');
            expect(validator('1001', {}, {}, context)).toBe('Number must be between 0 and 1000');
            expect(validator('-1', {}, {}, context)).toBe('Number must be between 0 and 1000');
        });

        it('should handle optional numeric fields', () => {
            const validator = validatorsModule.validKYCNumeric('amount', false, 0, 1000);
            const context = createMockContext();

            expect(validator('', {}, {}, context)).toBeNull();
            expect(validator(null, {}, {}, context)).toBeNull();
            expect(validator(undefined, {}, {}, context)).toBeNull();
        });
    });

    describe('validateKYCField (centralized validator)', () => {
        it('should trim values and apply custom validation', () => {
            const customValidator = (value: string) => value.length >= 3;
            const validator = validatorsModule.validateKYCField('field', {
                isRequired: true,
                minLength: 3,
                maxLength: 10,
                customValidator,
                errorMessage: 'Custom error',
            });
            const context = createMockContext();

            // Should trim and validate
            expect(validator('  abc  ', {}, {}, context)).toBeNull();
            expect(validator('  ab  ', {}, {}, context)).toBe('Custom error');
            expect(validator('  a  ', {}, {}, context)).toBe('Minimum 3 characters required');
        });

        it('should reject whitespace-only for both required and optional fields', () => {
            const requiredValidator = validatorsModule.validateKYCField('field', { isRequired: true });
            const optionalValidator = validatorsModule.validateKYCField('field', { isRequired: false });
            const context = createMockContext();

            expect(requiredValidator('   ', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
            expect(optionalValidator('   ', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
        });
    });
});
