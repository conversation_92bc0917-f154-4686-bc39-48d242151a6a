import { validators } from '@amille/simple-validators';
import { TFunction } from 'i18next';
import { trim } from 'lodash/fp';

// Mock translation function
const mockT: TFunction = (key: string, options?: any) => {
    if (key === 'common:formErrors.minLength') {
        return `Minimum ${options?.minLength} characters required`;
    }
    if (key === 'common:formErrors.maxLength') {
        return `Maximum ${options?.maxLength} characters allowed`;
    }
    return key;
};

// Mock context for validators
const createMockContext = (t?: TFunction) => ({
    defaultMessages: {
        requiredValue: 'This field is required.',
        noWhitespace: 'This field cannot be empty or whitespace only',
        invalidFormat: 'Invalid format',
        invalidNumber: 'Invalid number format',
    },
    outerContext: {
        t: t || mockT,
    },
});

// Test the core validation logic without importing the full module
const kycNameRegex = /^[A-Za-zÀ-ÖØ-öø-ÿ\u0100-\u017F\u0180-\u024F\u1E00-\u1EFF\u0400-\u04FF\u4E00-\u9FFF\u3040-\u309F\u30A0-\u30FF\u0600-\u06FF\s\-'.]+$/;
const kycAddressRegex = /^[A-Za-z0-9\s\-.,#/]+$/;

const validateKYCField = (
    field: string,
    options: {
        isRequired?: boolean;
        minLength?: number;
        maxLength?: number;
        customValidator?: (value: string) => boolean;
        errorMessage?: string;
    } = {}
) =>
    validators.custom(field, (value, values, errors, context) => {
        const { isRequired = true, minLength = 1, maxLength = 100, customValidator, errorMessage } = options;

        // Handle empty values for optional fields
        if (!isRequired && (value === null || value === undefined || value === '')) {
            return null; // Optional field can be empty
        }

        // Trim and check for whitespace-only
        const trimmedValue = trim(value);
        if (trimmedValue.length === 0) {
            return context.defaultMessages.noWhitespace; // Reject whitespace-only for both required and optional
        }

        // Check length constraints
        if (trimmedValue.length < minLength) {
            return (
                context.outerContext.t?.('common:formErrors.minLength', { minLength }) ||
                `Minimum ${minLength} characters required`
            );
        }

        if (trimmedValue.length > maxLength) {
            return (
                context.outerContext.t?.('common:formErrors.maxLength', { maxLength }) ||
                `Maximum ${maxLength} characters allowed`
            );
        }

        // Apply any custom field-specific validation
        if (customValidator && !customValidator(trimmedValue)) {
            return errorMessage || context.defaultMessages.invalidFormat || 'Invalid format';
        }

        return null;
    });

const validKYCName = (field: string, isRequired = true) =>
    validateKYCField(field, {
        isRequired,
        minLength: 1,
        maxLength: 100,
        customValidator: (value: string) => kycNameRegex.test(value),
        errorMessage: 'Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed',
    });

const validKYCAddress = (field: string, isRequired = true) =>
    validateKYCField(field, {
        isRequired,
        minLength: 1,
        maxLength: 200,
        customValidator: (value: string) => kycAddressRegex.test(value),
        errorMessage: 'Invalid address format. Only letters, numbers, spaces, and common punctuation are allowed',
    });

describe('KYC Field Validators', () => {
    describe('validKYCName', () => {
        it('should accept valid names with international characters', () => {
            const validator = validKYCName('name', true);
            const context = createMockContext();

            // Valid names
            expect(validator('John', {}, {}, context)).toBeNull();
            expect(validator('José María', {}, {}, context)).toBeNull();
            expect(validator("O'Connor", {}, {}, context)).toBeNull();
            expect(validator('Jean-Pierre', {}, {}, context)).toBeNull();
            expect(validator('李小明', {}, {}, context)).toBeNull();
            expect(validator('محمد', {}, {}, context)).toBeNull();
        });

        it('should reject whitespace-only inputs', () => {
            const validator = validKYCName('name', true);
            const context = createMockContext();

            expect(validator('   ', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
            expect(validator('\t\n', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
            expect(validator('', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
        });

        it('should reject invalid characters', () => {
            const validator = validKYCName('name', true);
            const context = createMockContext();

            expect(validator('John123', {}, {}, context)).toBe('Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed');
            expect(validator('John@Doe', {}, {}, context)).toBe('Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed');
        });

        it('should handle optional fields correctly', () => {
            const validator = validKYCName('name', false);
            const context = createMockContext();

            // Optional field can be empty
            expect(validator('', {}, {}, context)).toBeNull();
            expect(validator(null, {}, {}, context)).toBeNull();
            expect(validator(undefined, {}, {}, context)).toBeNull();

            // But still rejects whitespace-only
            expect(validator('   ', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
        });
    });

    describe('validKYCAddress', () => {
        it('should accept valid addresses', () => {
            const validator = validKYCAddress('address', true);
            const context = createMockContext();

            expect(validator('123 Main St', {}, {}, context)).toBeNull();
            expect(validator('Apt 4B, Building #5', {}, {}, context)).toBeNull();
            expect(validator('1234 Oak Ave.', {}, {}, context)).toBeNull();
        });

        it('should reject whitespace-only inputs', () => {
            const validator = validKYCAddress('address', true);
            const context = createMockContext();

            expect(validator('   ', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
        });
    });

    describe('validateKYCField (centralized validator)', () => {
        it('should trim values and apply custom validation', () => {
            const customValidator = (value: string) => value.length >= 3;
            const validator = validateKYCField('field', {
                isRequired: true,
                minLength: 3,
                maxLength: 10,
                customValidator,
                errorMessage: 'Custom error',
            });
            const context = createMockContext();

            // Should trim and validate
            expect(validator('  abc  ', {}, {}, context)).toBeNull();
            expect(validator('  ab  ', {}, {}, context)).toBe('Custom error');
            expect(validator('  a  ', {}, {}, context)).toBe('Minimum 3 characters required');
        });

        it('should reject whitespace-only for both required and optional fields', () => {
            const requiredValidator = validateKYCField('field', { isRequired: true });
            const optionalValidator = validateKYCField('field', { isRequired: false });
            const context = createMockContext();

            expect(requiredValidator('   ', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
            expect(optionalValidator('   ', {}, {}, context)).toBe('This field cannot be empty or whitespace only');
        });
    });

    describe('trimming functionality', () => {
        it('should properly trim leading and trailing whitespace', () => {
            expect(trim('  hello  ')).toBe('hello');
            expect(trim('\t\nworld\t\n')).toBe('world');
            expect(trim('   ')).toBe('');
        });

        it('should preserve internal whitespace', () => {
            expect(trim('  hello world  ')).toBe('hello world');
            expect(trim('  first  middle  last  ')).toBe('first  middle  last');
        });
    });
});
