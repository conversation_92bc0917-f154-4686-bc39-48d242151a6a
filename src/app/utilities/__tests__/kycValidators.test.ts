/**
 * Unit tests for enhanced KYC field validators
 * Tests the centralized validation system with proper optional field handling
 */

import { trim } from 'lodash';

// Define regex patterns used in validators (matching the actual implementation)
const whiteSpace = /^\s*$/;
const empty = /^$/;
const kycNameRegex =
    // eslint-disable-next-line max-len
    /^[A-Za-zÀ-ÖØ-öø-ÿ\u0100-\u017F\u0180-\u024F\u1E00-\u1EFF\u0400-\u04FF\u4E00-\u9FFF\u3040-\u309F\u30A0-\u30FF\u0600-\u06FF\s\-'.]+$/;
const kycAddressRegex = /^[A-Za-z0-9\s\-.,#/]+$/;
const kycBusinessNameRegex = /^[A-Za-z0-9\s\-.,#/&()]+$/;
const kycAlphanumericRegex = /^[A-Za-z0-9\s-]+$/;

// Mock validator functions to test the logic directly
const createKYCValidator =
    (customValidator?: (value: string) => boolean, errorMessage?: string) =>
    (value: string, values: any, errors: any, context: any) => {
        const input = trim(value);

        // Check for whitespace-only input or empty string
        if (whiteSpace.test(input) || empty.test(input)) {
            return context.defaultMessages.requiredValue;
        }

        // Apply field-specific validation if provided
        if (customValidator && !customValidator(input)) {
            return errorMessage || context.defaultMessages.invalidFormat || 'Invalid format';
        }

        return null;
    };

const validKYCName = createKYCValidator(
    value => kycNameRegex.test(value),
    'Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed'
);

const validKYCAddress = createKYCValidator(
    value => kycAddressRegex.test(value),
    'Invalid address format. Only letters, numbers, spaces, and common punctuation are allowed'
);

const validKYCBusinessName = createKYCValidator(
    value => kycBusinessNameRegex.test(value),
    'Invalid business name format'
);

const validKYCAlphanumeric = createKYCValidator(
    value => kycAlphanumericRegex.test(value),
    'Invalid format. Only letters, numbers, spaces, and hyphens are allowed'
);

const validKYCString = (value: string, values: any, errors: any, context: any) => {
    const input = trim(value);

    // Check for whitespace-only input or empty string
    if (whiteSpace.test(input) || empty.test(input)) {
        return context.defaultMessages.requiredValue;
    }

    return null;
};

// Mock context for validator testing
const mockContext = {
    defaultMessages: {
        requiredValue: 'This field is required',
        invalidFormat: 'Invalid format',
    },
};

describe('KYC Field Validators', () => {
    describe('validKYCName', () => {
        it('should accept valid names', () => {
            expect(validKYCName('John', {}, {}, mockContext)).toBe(null);
            expect(validKYCName('Mary-Jane', {}, {}, mockContext)).toBe(null);
            expect(validKYCName("O'Connor", {}, {}, mockContext)).toBe(null);
            expect(validKYCName('José María', {}, {}, mockContext)).toBe(null); // Unicode
            expect(validKYCName('Smith Jr.', {}, {}, mockContext)).toBe(null);
        });

        it('should reject whitespace-only inputs', () => {
            expect(validKYCName('   ', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCName('\t\t', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCName(' \n ', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCName('', {}, {}, mockContext)).toBe('This field is required');
        });

        it('should trim and accept valid input', () => {
            expect(validKYCName('  John  ', {}, {}, mockContext)).toBe(null);
            expect(validKYCName('\tMary\t', {}, {}, mockContext)).toBe(null);
        });

        it('should reject invalid characters', () => {
            expect(validKYCName('John123', {}, {}, mockContext)).toContain('Invalid name format');
            expect(validKYCName('John@Smith', {}, {}, mockContext)).toContain('Invalid name format');
            expect(validKYCName('John#Smith', {}, {}, mockContext)).toContain('Invalid name format');
        });

        it('should accept single character names', () => {
            expect(validKYCName('J', {}, {}, mockContext)).toBe(null);
            expect(validKYCName('李', {}, {}, mockContext)).toBe(null); // Chinese character
        });
    });

    describe('validKYCAddress', () => {
        it('should accept valid addresses', () => {
            expect(validKYCAddress('123 Main Street', {}, {}, mockContext)).toBe(null);
            expect(validKYCAddress('Apt 4B, Building C', {}, {}, mockContext)).toBe(null);
            expect(validKYCAddress('Unit #12-34', {}, {}, mockContext)).toBe(null);
            expect(validKYCAddress('123/45 Road Name', {}, {}, mockContext)).toBe(null);
        });

        it('should reject whitespace-only inputs', () => {
            expect(validKYCAddress('   ', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCAddress('\t\t', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCAddress('', {}, {}, mockContext)).toBe('This field is required');
        });

        it('should trim and accept valid input', () => {
            expect(validKYCAddress('  123 Main St  ', {}, {}, mockContext)).toBe(null);
        });

        it('should reject invalid characters', () => {
            expect(validKYCAddress('123 Main St @', {}, {}, mockContext)).toContain('Invalid address format');
            expect(validKYCAddress('123 Main St %', {}, {}, mockContext)).toContain('Invalid address format');
        });
    });

    describe('validKYCBusinessName', () => {
        it('should accept valid business names', () => {
            expect(validKYCBusinessName('ABC Corp Ltd.', {}, {}, mockContext)).toBe(null);
            expect(validKYCBusinessName('Smith & Associates', {}, {}, mockContext)).toBe(null);
            expect(validKYCBusinessName('Tech Solutions (Pvt) Ltd', {}, {}, mockContext)).toBe(null);
            expect(validKYCBusinessName('Company #123', {}, {}, mockContext)).toBe(null);
        });

        it('should reject whitespace-only inputs', () => {
            expect(validKYCBusinessName('   ', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCBusinessName('\t\t', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCBusinessName('', {}, {}, mockContext)).toBe('This field is required');
        });

        it('should trim and accept valid input', () => {
            expect(validKYCBusinessName('  ABC Corp  ', {}, {}, mockContext)).toBe(null);
        });
    });

    describe('validKYCAlphanumeric', () => {
        it('should accept valid alphanumeric inputs', () => {
            expect(validKYCAlphanumeric('ABC123', {}, {}, mockContext)).toBe(null);
            expect(validKYCAlphanumeric('Test-Value', {}, {}, mockContext)).toBe(null);
            expect(validKYCAlphanumeric('Value 123', {}, {}, mockContext)).toBe(null);
        });

        it('should reject whitespace-only inputs', () => {
            expect(validKYCAlphanumeric('   ', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCAlphanumeric('\t\t', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCAlphanumeric('', {}, {}, mockContext)).toBe('This field is required');
        });

        it('should trim and accept valid input', () => {
            expect(validKYCAlphanumeric('  ABC123  ', {}, {}, mockContext)).toBe(null);
        });

        it('should reject invalid characters', () => {
            expect(validKYCAlphanumeric('ABC@123', {}, {}, mockContext)).toContain('Invalid format');
            expect(validKYCAlphanumeric('ABC#123', {}, {}, mockContext)).toContain('Invalid format');
        });
    });

    describe('validKYCString', () => {
        it('should accept any non-whitespace string', () => {
            expect(validKYCString('Any string', {}, {}, mockContext)).toBe(null);
            expect(validKYCString('123', {}, {}, mockContext)).toBe(null);
            expect(validKYCString('Special@Characters#', {}, {}, mockContext)).toBe(null);
        });

        it('should reject whitespace-only inputs', () => {
            expect(validKYCString('   ', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCString('\t\t', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCString(' \n ', {}, {}, mockContext)).toBe('This field is required');
            expect(validKYCString('', {}, {}, mockContext)).toBe('This field is required');
        });

        it('should trim and accept valid input', () => {
            expect(validKYCString('  Valid String  ', {}, {}, mockContext)).toBe(null);
        });
    });

    describe('requiredNonEmptyStringWithOptions', () => {
        it('should work with custom validator', () => {
            const customValidator = createKYCValidator(
                (value: string) => value.length >= 3,
                'Minimum 3 characters required'
            );

            expect(customValidator('ab', {}, {}, mockContext)).toBe('Minimum 3 characters required');
            expect(customValidator('abc', {}, {}, mockContext)).toBe(null);
            expect(customValidator('   ', {}, {}, mockContext)).toBe('This field is required');
        });

        it('should work without custom validator', () => {
            const basicValidator = createKYCValidator();

            expect(basicValidator('valid', {}, {}, mockContext)).toBe(null);
            expect(basicValidator('   ', {}, {}, mockContext)).toBe('This field is required');
        });
    });
});

describe('KYC Validation Integration Tests', () => {
    describe('Field-specific validation scenarios', () => {
        it('should handle international names correctly', () => {
            // Test various international characters
            const internationalNames = [
                'José María', // Spanish
                'François', // French
                'Müller', // German
                'Øyvind', // Norwegian
                'Владимир', // Russian
                '田中太郎', // Japanese
                '李小明', // Chinese
                'محمد', // Arabic
            ];

            internationalNames.forEach(name => {
                expect(validKYCName(name, {}, {}, mockContext)).toBe(null);
            });
        });

        it('should handle edge cases for addresses', () => {
            const validAddresses = [
                '1', // Single character
                'A', // Single letter
                '123', // Numbers only
                'Street', // Letters only
                '123 Main St.', // Standard address
                'Apt 4B, Bldg C', // Apartment
                'Unit #12-34/56', // Complex unit number
            ];

            validAddresses.forEach(address => {
                expect(validKYCAddress(address, {}, {}, mockContext)).toBe(null);
            });
        });

        it('should handle business name variations', () => {
            const validBusinessNames = [
                'ABC Corp',
                'Smith & Associates',
                'Tech Solutions (Pvt) Ltd',
                'Company #123',
                'Business & Co.',
                'Firm (International)',
            ];

            validBusinessNames.forEach(name => {
                expect(validKYCBusinessName(name, {}, {}, mockContext)).toBe(null);
            });
        });
    });

    describe('Whitespace rejection scenarios', () => {
        const validatorsToTest = [
            { name: 'validKYCName', validator: validKYCName },
            { name: 'validKYCAddress', validator: validKYCAddress },
            { name: 'validKYCBusinessName', validator: validKYCBusinessName },
            { name: 'validKYCAlphanumeric', validator: validKYCAlphanumeric },
            { name: 'validKYCString', validator: validKYCString },
        ];

        const whitespaceInputs = [
            '   ', // Spaces
            '\t\t', // Tabs
            '\n\n', // Newlines
            ' \t \n ', // Mixed whitespace
            '', // Empty string
        ];

        validatorsToTest.forEach(({ name, validator }) => {
            it(`${name} should reject all whitespace-only inputs`, () => {
                whitespaceInputs.forEach(input => {
                    expect(validator(input, {}, {}, mockContext)).toBe('This field is required');
                });
            });
        });
    });
});

describe('Optional Field Validation Tests', () => {
    // Create validators that support optional field behavior
    const createOptionalValidator =
        (customValidator?: (value: string) => boolean, errorMessage?: string) =>
        (value: string, values: any, errors: any, context: any, isRequired = true) => {
            // Handle empty values for optional fields
            if (!isRequired && (value === null || value === undefined || value === '')) {
                return null; // Optional field can be empty
            }

            // Trim and check for whitespace-only
            const trimmedValue = trim(value);
            if (trimmedValue.length === 0) {
                return context.defaultMessages.requiredValue; // Reject whitespace-only for both required and optional
            }

            // Apply any custom field-specific validation
            if (customValidator && !customValidator(trimmedValue)) {
                return errorMessage || context.defaultMessages.invalidFormat || 'Invalid format';
            }

            return null;
        };

    const optionalKYCName = createOptionalValidator(
        value => kycNameRegex.test(value),
        'Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed'
    );

    describe('Required vs Optional Field Behavior', () => {
        it('should handle required fields correctly', () => {
            // Required field - empty input should fail
            expect(optionalKYCName('', {}, {}, mockContext, true)).toBe('This field is required');
            expect(optionalKYCName('   ', {}, {}, mockContext, true)).toBe('This field is required');
            expect(optionalKYCName('John', {}, {}, mockContext, true)).toBe(null);
        });

        it('should handle optional fields correctly', () => {
            // Optional field - empty input should pass
            expect(optionalKYCName('', {}, {}, mockContext, false)).toBe(null);
            expect(optionalKYCName(null, {}, {}, mockContext, false)).toBe(null);
            expect(optionalKYCName(undefined, {}, {}, mockContext, false)).toBe(null);

            // Optional field - whitespace-only should still fail
            expect(optionalKYCName('   ', {}, {}, mockContext, false)).toBe('This field is required');
            expect(optionalKYCName('\t\t', {}, {}, mockContext, false)).toBe('This field is required');

            // Optional field - valid content should pass
            expect(optionalKYCName('John', {}, {}, mockContext, false)).toBe(null);
        });
    });

    describe('Field-specific validation with optional behavior', () => {
        it('should validate name format for optional fields', () => {
            // Valid names should pass
            expect(optionalKYCName('José María', {}, {}, mockContext, false)).toBe(null);
            expect(optionalKYCName('李小明', {}, {}, mockContext, false)).toBe(null);

            // Invalid names should fail
            expect(optionalKYCName('John123', {}, {}, mockContext, false)).toContain('Invalid name format');
        });
    });
});

describe('Numeric Field Validation Tests', () => {
    const createNumericValidator =
        (minValue = 0, maxValue = 999999999) =>
        (value: string, values: any, errors: any, context: any, isRequired = true) => {
            // Handle empty values for optional fields
            if (!isRequired && (value === null || value === undefined || value === '')) {
                return null;
            }

            // Check if value is a valid number
            const numValue = Number(value);
            if (Number.isNaN(numValue)) {
                return context.defaultMessages.invalidNumber || 'Invalid number format';
            }

            // Check range constraints
            if (numValue < minValue || numValue > maxValue) {
                return `Number must be between ${minValue} and ${maxValue}`;
            }

            return null;
        };

    const numericValidator = createNumericValidator(0, 999999999);

    describe('Numeric validation', () => {
        it('should accept valid numbers', () => {
            expect(numericValidator('123', {}, {}, mockContext, true)).toBe(null);
            expect(numericValidator('0', {}, {}, mockContext, true)).toBe(null);
            expect(numericValidator('999999999', {}, {}, mockContext, true)).toBe(null);
        });

        it('should reject invalid numbers', () => {
            expect(numericValidator('abc', {}, {}, mockContext, true)).toBe('Invalid number format');
            expect(numericValidator('12.34.56', {}, {}, mockContext, true)).toBe('Invalid number format');
        });

        it('should enforce range constraints', () => {
            expect(numericValidator('-1', {}, {}, mockContext, true)).toContain('Number must be between');
            expect(numericValidator('1000000000', {}, {}, mockContext, true)).toContain('Number must be between');
        });

        it('should handle optional numeric fields', () => {
            expect(numericValidator('', {}, {}, mockContext, false)).toBe(null);
            expect(numericValidator('123', {}, {}, mockContext, false)).toBe(null);
        });
    });
});
