import { Input } from 'antd';
import { SearchProps } from 'antd/lib/input/Search';
import { memo } from 'react';
import styled from 'styled-components';

const StyledSearch = styled(Input.Search)`
    height: 48px;
    padding: 8px;
    font-size: var(--input-font-size, 1rem);
    border: 2px solid #6b6d70;
    border-radius: 4px;
    background-color: transparent;

    :hover {
        border-color: black;
    }

    :has(.ant-input:focus),
    :has(.ant-input-focused) {
        outline-color: rgb(98, 102, 105);
    }

    .ant-input-group {
        bottom: 2px;
    }

    & .ant-input-group > .ant-input:first-child {
        border: none;
        box-shadow: none;
        font-size: var(--input-font-size, 1rem);
    }

    & > .ant-input-group > .ant-input-group-addon:last-child .ant-input-search-button:not(.ant-btn-primary) {
        border: none;
        box-shadow: none;

        :hover,
        :focus {
            border-color: none;
            box-shadow: none;
        }
    }
    .ant-input {
        background-color: transparent;
    }

    .ant-input-group-addon {
        background-color: transparent;
    }

    .ant-input-search-button {
        padding-right: 8px;
        padding-bottom: 4px !important;
        font-size: 1rem;
        color: #6b6d70;
        border-color: transparent;
        background: transparent;
    }
`;

const Search = (props: SearchProps) => <StyledSearch {...props} />;

export default memo(Search);
