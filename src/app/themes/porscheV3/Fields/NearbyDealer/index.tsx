import { components<PERSON><PERSON>y, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>rapper, <PERSON><PERSON><PERSON>, PTag } from '@porsche-design-system/components-react';
import { useField } from 'formik';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { JourneyEventDataFragment } from '../../../../api/fragments/JourneyEventData';
import { useGetNearbyDealersQuery } from '../../../../api/queries/getNearbyDealers';
import { NearbyDealerFilteringRule } from '../../../../api/types';
import { useCompany } from '../../../../components/contexts/CompanyContextManager';
import FormItem from '../../../../components/fields/FormItem';
import useTranslatedString from '../../../../utilities/useTranslatedString';
import DealerInfo from './DealerInfo';

const StyledDiv = styled.div`
    position: relative;
`;

const StyledPTextField = styled(PTextFieldWrapper)`
    position: relative;
    z-index: 1;

    &:hover + div.option-container,
    &:has(input:focus) + div.option-container {
        border-color: #010205;
    }
`;

const StyledOption = styled.div`
    display: flex;
    gap: 16px;
    align-items: center;
    padding: 8px 12px 8px 12px;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
        background: #d8d8db;
    }
`;

const StyledOptionContainer = styled.div`
    position: relative;
    z-index: 2;
    background: #fff;
    border: 2px solid #6b6d70;
    border-top: none;
    border-radius: 4px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    background: white;

    &:hover {
        border-color: #010205;
    }

    margin-top: -3px;

    div.line {
        padding-top: 3px;
        border: none;
        border-bottom: 1px solid #6b6d70;
    }

    div.content {
        padding: 8px;
        display: flex;
        flex-direction: column;
        gap: 8px;

        overflow-wrap: break-word;

        max-height: 160px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;

        scrollbar-width: thin;
        scrollbar-color: auto;

        .no-results {
            padding: 8px 12px 8px 12px;
        }
    }
`;

type NearbyDealerProps = {
    event: JourneyEventDataFragment;
    required: boolean;
    name: string;
    label: string;
};

const NearbyDealer = ({ event, label, name, required, ...props }: NearbyDealerProps) => {
    const translatedString = useTranslatedString();
    const { t } = useTranslation(['common', 'eventApplicantForm']);
    const [searchText, setSearchText] = useState<string>('');
    const [isOpen, setIsOpen] = useState<boolean>(false);

    const wrapperRef = useRef<HTMLDivElement>(null);
    const inputRef = useRef<HTMLInputElement>(null);

    const company = useCompany(true);

    const [field, meta, { setValue, setTouched }] = useField({ name });

    const filter = useMemo(
        (): NearbyDealerFilteringRule => ({
            companyId: event.module.company.id,
            productionOnly: !event.privateAccess,
            searchText,
        }),
        [event.module.company.id, event.privateAccess, searchText]
    );

    const { data, loading } = useGetNearbyDealersQuery({
        fetchPolicy: 'cache-and-network',
        variables: { filter: { ...filter, companyId: company?.id, searchText } },
    });

    const dealers = useMemo(() => {
        if (loading) {
            return [];
        }

        const dealersFromApi = data?.getNearbyDealers || [];

        if (event.dealerIds.length > 0) {
            const filteredDealers = dealersFromApi.filter(dealer =>
                event.dealers.some(dealerEvent => dealerEvent.id === dealer.id)
            );

            return filteredDealers;
        }

        return [];
    }, [data?.getNearbyDealers, event.dealerIds.length, event.dealers, loading]);

    // select dealer option handler
    const handleDealerOptionClick = useCallback(
        dealer => {
            // set field value
            setValue(dealer.id);
            // close dropdown
            setIsOpen(false);
        },
        [setValue]
    );

    // dealer options
    const options = useMemo(
        () =>
            dealers.map(dealer => {
                const distance = dealer.calculatedDistance ? `${Math.round(dealer.calculatedDistance)} km` : '';

                return (
                    <StyledOption key={dealer.id} onClick={() => handleDealerOptionClick(dealer)}>
                        {distance && <PTag color="notification-info-soft">{distance}</PTag>}
                        <div>
                            <PText>{translatedString(dealer.legalName)}</PText>

                            {distance && dealer.contact.address?.defaultValue && (
                                <PText color="contrast-high" size="x-small">
                                    {translatedString(dealer.contact.address)}
                                </PText>
                            )}
                        </div>
                    </StyledOption>
                );
            }),
        [dealers, handleDealerOptionClick, translatedString]
    );

    // handle search button click
    const handleSearchButtonClick = useCallback((e: MouseEvent) => {
        e.stopPropagation();
        e.preventDefault();

        setSearchText(inputRef.current?.value || '');
    }, []);

    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            // Text field
            const textField = wrapper.querySelector('p-text-field-wrapper') as HTMLDivElement;
            const textFieldShadowRoot = textField?.shadowRoot;

            const textFieldWrapper = textFieldShadowRoot?.querySelector('.wrapper') as HTMLDivElement;
            if (textFieldWrapper) {
                textFieldWrapper.style.background = 'white';
            }

            // Add event listener to the search button inside the text field
            const buttonPureShadowRoot = textFieldShadowRoot?.querySelector('.wrapper > p-button-pure')?.shadowRoot;
            const button = buttonPureShadowRoot?.querySelector('button');
            if (button) {
                button.addEventListener('click', handleSearchButtonClick);
            }

            // Cleanup on unmount or dependency change
            return () => {
                if (button) {
                    button.removeEventListener('click', handleSearchButtonClick);
                }
            };
        });
    }, [handleSearchButtonClick]);

    // Handle input change to clear search text and close dropdown if input is empty
    const onChangeInput = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            if (!inputRef.current.value) {
                setSearchText('');
                setIsOpen(false);
                setValue(undefined);
            }
        },
        [setValue]
    );

    // Handle input click to open dropdown and clear selected dealer
    const onClickInput = useCallback(() => {
        setValue(undefined);
        setTouched(false);
        setIsOpen(true);
    }, [setTouched, setValue]);

    const { hasError, errorMessage } = useMemo(
        () => ({
            hasError: !!meta?.error && meta?.touched,
            errorMessage: Array.isArray(meta?.error) ? '' : meta?.error,
        }),
        [meta?.error, meta?.touched]
    );

    const dealer = useMemo(() => dealers.find(dealer => dealer.id === field.value) || null, [dealers, field.value]);

    return (
        <FormItem {...props}>
            <>
                <StyledDiv ref={wrapperRef}>
                    <StyledPTextField label={label} state={hasError ? 'error' : undefined}>
                        <input
                            ref={inputRef}
                            onChange={onChangeInput}
                            onClick={onClickInput}
                            placeholder={t('eventApplicantForm:fields.findNearbyDealer.placeholder')}
                            required={required}
                            type="search"
                        />
                        {hasError && <span slot="message">{errorMessage}</span>}
                    </StyledPTextField>

                    {!loading && !hasError && isOpen && (
                        <StyledOptionContainer className="option-container">
                            <div className="line" />
                            <div className="content">
                                {options.length === 0 && (
                                    <div className="no-results">
                                        <PText>{t('common:noResults')}</PText>
                                    </div>
                                )}

                                {options.length > 0 && options}
                            </div>
                        </StyledOptionContainer>
                    )}
                </StyledDiv>

                {dealer && <DealerInfo dealer={dealer} />}
            </>
        </FormItem>
    );
};

export default NearbyDealer;
