import { PText } from '@porsche-design-system/components-react';
import { Space } from 'antd';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { DealerJourneyDataFragment } from '../../../../api/fragments/DealerJourneyData';
import useTranslatedString from '../../../../utilities/useTranslatedString';

const StyledSpace = styled(Space)`
    margin-top: 10px;
`;

type DealerInfoProps = {
    dealer: DealerJourneyDataFragment;
};

const DealerInfo = ({ dealer }: DealerInfoProps) => {
    const { t } = useTranslation('configuratorJourney');
    const translatedString = useTranslatedString();

    return (
        <StyledSpace direction="vertical" size={4}>
            <PText size="medium" weight="bold">
                {translatedString(dealer.legalName)}
            </PText>

            {dealer.contact.address?.defaultValue && <PText>{translatedString(dealer.contact.address)}</PText>}

            {dealer.contact.telephone && (
                <PText>{t('configuratorJourney:thankyou.contents.dealerInfo.phone', dealer.contact.telephone)}</PText>
            )}

            {dealer.contact.email && (
                <PText>
                    {t('configuratorJourney:thankyou.contents.dealerInfo.email', {
                        email: dealer.contact.email,
                    })}
                </PText>
            )}
        </StyledSpace>
    );
};

export default DealerInfo;
