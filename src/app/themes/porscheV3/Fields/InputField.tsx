import { componentsReady } from '@porsche-design-system/components-react';
import { Form } from 'antd';
import { useField } from 'formik';
import { isEqual, trim } from 'lodash/fp';
import { useCallback, useEffect, useRef } from 'react';
import { InputFieldProps } from '../../../components/fields/InputField';
import { StyledTextFieldWrapper } from './shared';
import useRenderTooltip from './useRenderTooltip';
import { getTextFieldState } from './utils';

const useOverrideStyle = (wrapperRef: React.RefObject<HTMLDivElement>) => {
    useEffect(() => {
        if (!wrapperRef.current) {
            return;
        }

        const wrapper = wrapperRef.current;

        componentsReady(wrapper).then(() => {
            const elm = wrapper.querySelector('p-text-field-wrapper');
            const elmShaRoot = elm?.shadowRoot;
            if (!elm || !elmShaRoot) {
                return;
            }

            const rootContainer = elmShaRoot.querySelector('.root');
            const description = rootContainer.querySelector('#description');
            if (description) {
                rootContainer.setAttribute('style', 'gap:2px');
            }
        });
    }, [wrapperRef]);
};

const InputField = ({
    type = 'text',
    placeholder,
    status,
    label,
    required = false,
    name,
    itemProps,
    tooltip,
    disabled,
    maxLength,
    onKeyPress,
    removeWhiteSpace,
    trimOnBlur = true,
}: InputFieldProps) => {
    const [field, meta, helper] = useField({ name });

    const renderTooltip = useRenderTooltip(tooltip);

    const wrapperRef = useRef<HTMLDivElement>(null);

    useOverrideStyle(wrapperRef);

    useEffect(() => {
        if (removeWhiteSpace && typeof field.value === 'string') {
            helper.setValue(field.value.replace(/\s/g, ''));
        }
    }, [removeWhiteSpace, field.value, helper]);

    // Handle trimming on blur for KYC fields (unless they have removeWhiteSpace)
    const handleBlur = useCallback(
        (event: React.FocusEvent<HTMLInputElement>) => {
            // Call the original onBlur first
            field.onBlur(event);

            // Apply trimming if enabled and not using removeWhiteSpace
            if (trimOnBlur && !removeWhiteSpace && typeof field.value === 'string') {
                const trimmedValue = trim(field.value);
                if (trimmedValue !== field.value) {
                    helper.setValue(trimmedValue);
                }
            }
        },
        [field, trimOnBlur, removeWhiteSpace, helper]
    );

    const hasError = !!meta?.error && (meta?.touched || !isEqual(meta?.initialValue, meta?.value));
    const errorMessage = Array.isArray(meta?.error) ? '' : meta?.error;

    return (
        <div ref={wrapperRef}>
            <Form.Item {...itemProps} required={required}>
                <StyledTextFieldWrapper state={hasError ? 'error' : getTextFieldState(status)}>
                    {label && (
                        <span slot="label">
                            {label}
                            {renderTooltip()}
                        </span>
                    )}
                    {itemProps?.help && <span slot="description">{itemProps?.help}</span>}
                    <input
                        {...field}
                        disabled={disabled}
                        maxLength={maxLength}
                        onBlur={handleBlur}
                        onKeyPress={onKeyPress}
                        placeholder={placeholder}
                        required={required}
                        type={type}
                        value={field.value || ''}
                    />
                    {hasError && <span slot="message">{errorMessage}</span>}
                </StyledTextFieldWrapper>
            </Form.Item>
        </div>
    );
};

export default InputField;
