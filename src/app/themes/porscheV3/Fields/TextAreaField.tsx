import { PTextareaWrapper } from '@porsche-design-system/components-react';
import { Form } from 'antd';
import { useField } from 'formik';
import { isEqual, trim } from 'lodash/fp';
import { useCallback } from 'react';
import { TextAreaFieldProps } from '../../../components/fields/TextAreaField';
import useRenderTooltip from './useRenderTooltip';
import { getTextFieldState } from './utils';

const TextAreaField = ({
    name,
    label,
    placeholder,
    maxLength,
    disabled,
    showCount,
    tooltip,
    required,
    status,
    trimOnBlur = true,
}: TextAreaFieldProps) => {
    const [field, meta, helper] = useField({ name });

    // Handle trimming on blur for KYC fields
    const handleBlur = useCallback(
        (event: React.FocusEvent<HTMLTextAreaElement>) => {
            // Call the original onBlur first
            field.onBlur(event);

            // Apply trimming if enabled
            if (trimOnBlur && typeof field.value === 'string') {
                const trimmedValue = trim(field.value);
                if (trimmedValue !== field.value) {
                    helper.setValue(trimmedValue);
                }
            }
        },
        [field, trimOnBlur, helper]
    );

    const renderTooltip = useRenderTooltip(tooltip);

    const hasError = !!meta?.error && (meta?.touched || !isEqual(meta?.initialValue, meta?.value));
    const errorMessage = Array.isArray(meta?.error) ? '' : meta?.error;

    return (
        <Form.Item name={name} required={required}>
            <PTextareaWrapper
                hideLabel={!label}
                showCounter={!!showCount}
                state={hasError ? 'error' : getTextFieldState(status)}
            >
                {label && (
                    <span id="textarea-label" slot="label">
                        {label}
                        {renderTooltip()}
                    </span>
                )}
                <textarea
                    {...field}
                    disabled={disabled}
                    maxLength={maxLength}
                    name={name}
                    onBlur={handleBlur}
                    placeholder={placeholder}
                    required={required}
                    value={field.value || ''}
                />
                {hasError && <span slot="message">{errorMessage}</span>}
            </PTextareaWrapper>
        </Form.Item>
    );
};

export default TextAreaField;
