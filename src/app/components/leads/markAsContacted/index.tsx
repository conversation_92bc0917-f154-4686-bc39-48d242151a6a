import { ApolloError, useApolloClient } from '@apollo/client';
import { Typography } from 'antd';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { LaunchPadModuleSpecsFragment } from '../../../api/fragments/LaunchPadModuleSpecs';
import { LeadDataFragment } from '../../../api/fragments/LeadData';
import {
    MarkAsContactedDocument,
    MarkAsContactedMutationVariables,
    MarkAsContactedMutation,
} from '../../../api/mutations/markAsContacted';
import { useThemeComponents } from '../../../themes/hooks';

const Container = styled.div`
    margin-bottom: 32px;
`;

type UseMaskAsContactedModalProps = {
    launchpadModule: LaunchPadModuleSpecsFragment;
    lead: LeadDataFragment;
    dealerId: string;
    refetchLead?: () => void;
};

type MaskAsContactedModalProps = UseMaskAsContactedModalProps & {
    visible: boolean;
    onClose?: (isSave: boolean) => void;
};

const MaskAsContactedModal = ({ lead, onClose, visible, ...props }: MaskAsContactedModalProps) => {
    const [submissionLoading, setSubmissionLoading] = useState(false);

    const { t } = useTranslation(['launchpadLeadDetails']);
    const { Button, Modal, notification } = useThemeComponents();
    const apolloClient = useApolloClient();

    const onCancel = useCallback(() => {
        onClose(false);
    }, [onClose]);

    const markAsContactedAction = useCallback(async () => {
        try {
            setSubmissionLoading(true);
            notification.loading({
                content: t('launchpadLeadDetails:markAsContactedModal.updatingContact'),
                duration: 0,
                key: 'primary',
            });

            const { errors } = await apolloClient.mutate<MarkAsContactedMutation, MarkAsContactedMutationVariables>({
                mutation: MarkAsContactedDocument,
                variables: {
                    leadId: lead.id,
                },
            });

            if (!errors) {
                notification.success({
                    content: t('launchpadLeadDetails:markAsContactedModal.submissionCompleteMessage'),
                    key: 'primary',
                });

                onClose(true);
            }
        } catch (error) {
            if (error instanceof ApolloError) {
                notification.error(error.graphQLErrors[0].message);
            } else {
                console.error(error);
            }
        } finally {
            notification.destroy('primary');
            setSubmissionLoading(false);
        }
    }, [apolloClient, lead, notification, onClose, t]);

    const footerButton = useMemo(
        () => [
            <Button
                key="submit-contacted"
                onClick={e => {
                    e.preventDefault();
                    markAsContactedAction();
                }}
                type="primary"
                block
            >
                {t('launchpadLeadDetails:markAsContactedModal.buttons.submit')}
            </Button>,
            <Button key="cancel" onClick={onCancel} type="tertiary" block>
                {t('launchpadLeadDetails:markAsContactedModal.buttons.cancel')}
            </Button>,
        ],
        [Button, t, onCancel, markAsContactedAction]
    );

    return (
        <Modal
            className="launchpad-modal"
            closable={false}
            footer={footerButton}
            maskClosable={!submissionLoading}
            onCancel={onCancel}
            open={visible}
            title={t('launchpadLeadDetails:markAsContactedModal.title')}
            centered
            destroyOnClose
        >
            <Container>
                <Typography.Text>{t('launchpadLeadDetails:markAsContactedModal.confirmationMessage')}</Typography.Text>
            </Container>
        </Modal>
    );
};

export default MaskAsContactedModal;

export const useMarkAsContactedModal = ({ refetchLead, ...props }: UseMaskAsContactedModalProps) => {
    const [visible, setVisible] = useState(false);

    const actions = useMemo(
        () => ({
            open: () => setVisible(true),
            close: (isSaved: boolean) => {
                setVisible(false);
                if (isSaved) {
                    refetchLead?.();
                }
            },
        }),
        [refetchLead]
    );

    return {
        ...actions,
        render: () => <MaskAsContactedModal onClose={actions.close} visible={visible} {...props} />,
    };
};
