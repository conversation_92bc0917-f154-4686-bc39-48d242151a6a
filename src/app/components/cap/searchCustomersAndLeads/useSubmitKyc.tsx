import { ApolloError } from '@apollo/client';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useConfirmBookingApplicationMutation } from '../../../api/mutations/confirmBookingApplication';
import { useQualifyLeadWithCapValuesMutation } from '../../../api/mutations/qualifyLeadWithCapValues';
import { useResubmitLeadToCapMutation } from '../../../api/mutations/resubmitLeadToCap';
import { useUploadLeadDocumentMutation } from '../../../api/mutations/uploadLeadDocument';
import refineCustomerValues from '../../../pages/shared/refineCustomerValues';
import { useThemeComponents } from '../../../themes/hooks';
import { prepareKYCFieldPayload } from '../../../utilities/kycPresets';
import useHandleError from '../../../utilities/useHandleError';
import { useContactKycConfirmationModal } from './ContactKycSubmission/useContactKycConfirmationModal';
import type { ContactKycFormProps } from './types';
import { SubmissionPurpose } from './types';
import type { StateAndDispatch } from './useReducer';

type UseSubmitKycProps = {
    onClose: (navigateBack?: boolean) => void;
} & StateAndDispatch;

const useSubmitKyc = ({ state, dispatch, onClose }: UseSubmitKycProps) => {
    const {
        lead,
        loading: { loadingBP, loadingLead },
        supportingApplicationValue: { applicationId, stage },
        submissionPurpose,
        selectedBusinessPartner,
        selectedLead,
        selectedSalesperson,
    } = state;

    const { t } = useTranslation(['applicationDetails']);
    const { notification } = useThemeComponents();
    const contactKYCConfirmationModal = useContactKycConfirmationModal();

    const [qualifyLeadMutation] = useQualifyLeadWithCapValuesMutation();
    const [confirmBookingMutation] = useConfirmBookingApplicationMutation();
    const [resubmitLeadToCap] = useResubmitLeadToCapMutation();
    const [uploadLeadDocument] = useUploadLeadDocumentMutation();

    const submitKyc = useHandleError<ContactKycFormProps>(
        async values => {
            try {
                const { id: leadId } = lead;
                dispatch({
                    type: 'setLoading',
                    loading: { loadingBP, loadingLead, submissionLoading: true },
                });
                notification.loading({
                    content: t('applicationDetails:messages.qualifyApplication'),
                    duration: 0,
                    key: 'primary',
                });

                const { customerAssetArray } = refineCustomerValues(values.customer.fields);
                const documentPayloads = customerAssetArray
                    .map(asset =>
                        asset.files
                            .map(file => {
                                if (file instanceof File) {
                                    return { upload: file, kind: asset.type };
                                }

                                return null;
                            })
                            .filter(Boolean)
                    )
                    .flat();

                const mutation = () => {
                    const commonSubmissionValue = {
                        capValues: {
                            businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                            leadGuid: selectedLead?.leadGuid,
                        },
                        updatedKyc: prepareKYCFieldPayload(values.customer.fields),
                        updatedTradeInVehicle: values.tradeInVehicle,
                    };

                    switch (submissionPurpose) {
                        case SubmissionPurpose.Qualify:
                            return qualifyLeadMutation({
                                variables: {
                                    leadId,
                                    selectedResponsibleSalesId: selectedSalesperson,
                                    ...commonSubmissionValue,
                                },
                            });

                        case SubmissionPurpose.ConfirmBooking:
                            return confirmBookingMutation({
                                variables: {
                                    applicationId,
                                    stage,
                                    ...commonSubmissionValue,
                                },
                            });

                        case SubmissionPurpose.Resubmission:
                            return resubmitLeadToCap({
                                variables: {
                                    leadId,
                                    isTestDrive: false,
                                    ...commonSubmissionValue,
                                },
                            });

                        default:
                            return null;
                    }
                };

                if (mutation) {
                    // First qualify the lead
                    const { errors } = await mutation();

                    if (!errors) {
                        // After successful qualification, upload documents
                        if (documentPayloads.length > 0) {
                            await Promise.all(
                                documentPayloads.map(payload =>
                                    uploadLeadDocument({
                                        variables: {
                                            leadId: lead.id,
                                            upload: payload.upload,
                                            kind: payload.kind,
                                            isTemporary: false,
                                        },
                                    })
                                )
                            );
                        }

                        notification.success({
                            content: t('applicationDetails:messages.qualifiedApplication'),
                            key: 'primary',
                        });

                        // after successful submission, close the modal
                        // go to details page, details page is currently same place
                        // once we the CI router for lead, then we need to redirect there
                        contactKYCConfirmationModal.close();
                        onClose(true);
                    }
                }
            } catch (error) {
                if (error instanceof ApolloError) {
                    notification.error(error.graphQLErrors[0].message);
                } else {
                    console.error(error);
                }
            } finally {
                notification.destroy('primary');
                dispatch({
                    type: 'setLoading',
                    loading: { loadingBP, loadingLead, submissionLoading: false },
                });
            }
        },
        [
            dispatch,
            loadingBP,
            loadingLead,
            notification,
            t,
            selectedBusinessPartner?.businessPartnerGuid,
            selectedLead?.leadGuid,
            submissionPurpose,
            qualifyLeadMutation,
            lead,
            selectedSalesperson,
            confirmBookingMutation,
            applicationId,
            stage,
            resubmitLeadToCap,
            contactKYCConfirmationModal,
            onClose,
            uploadLeadDocument,
        ]
    );

    const submitWithoutKyc = useCallback(async () => {
        try {
            const { id: leadId } = lead;
            dispatch({
                type: 'setLoading',
                loading: { loadingBP, loadingLead, submissionLoading: true },
            });
            notification.loading({
                content: t('applicationDetails:messages.qualifyApplication'),
                duration: 0,
                key: 'primary',
            });

            const commonSubmissionValue = {
                capValues: {
                    businessPartnerGuid: selectedBusinessPartner?.businessPartnerGuid,
                    leadGuid: selectedLead?.leadGuid,
                },
                updatedKyc: prepareKYCFieldPayload({}), // Empty KYC values
                updatedTradeInVehicle: [],
            };

            const mutation = () => {
                switch (submissionPurpose) {
                    case SubmissionPurpose.Qualify:
                        return qualifyLeadMutation({
                            variables: {
                                leadId,
                                selectedResponsibleSalesId: selectedSalesperson,
                                ...commonSubmissionValue,
                            },
                        });

                    case SubmissionPurpose.ConfirmBooking:
                        return confirmBookingMutation({
                            variables: {
                                applicationId,
                                stage,
                                ...commonSubmissionValue,
                            },
                        });

                    case SubmissionPurpose.Resubmission:
                        return resubmitLeadToCap({
                            variables: {
                                leadId,
                                isTestDrive: false,
                                ...commonSubmissionValue,
                            },
                        });

                    default:
                        return null;
                }
            };

            if (mutation) {
                const { errors } = await mutation();

                if (!errors) {
                    notification.success({
                        content: t('applicationDetails:messages.qualifiedApplication'),
                        key: 'primary',
                    });

                    // Close the modal and navigate
                    contactKYCConfirmationModal.close();
                    onClose(true);
                }
            }
        } catch (error) {
            if (error instanceof ApolloError) {
                notification.error(error.graphQLErrors[0].message);
            } else {
                console.error('Error in submitWithoutKyc:', error);
            }
        } finally {
            notification.destroy('primary');
            dispatch({
                type: 'setLoading',
                loading: { loadingBP, loadingLead, submissionLoading: false },
            });
        }
    }, [
        lead,
        dispatch,
        loadingBP,
        loadingLead,
        notification,
        t,
        selectedBusinessPartner?.businessPartnerGuid,
        selectedLead?.leadGuid,
        submissionPurpose,
        qualifyLeadMutation,
        selectedSalesperson,
        confirmBookingMutation,
        applicationId,
        stage,
        resubmitLeadToCap,
        contactKYCConfirmationModal,
        onClose,
    ]);

    return { submitKyc, submitWithoutKyc };
};

export default useSubmitKyc;
