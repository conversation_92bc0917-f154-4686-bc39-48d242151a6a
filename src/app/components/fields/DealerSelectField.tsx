import { type SelectProps } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { DealerOptionsDataFragment } from '../../api/fragments/DealerOptionsData';
import { useGetDealersOptionsQuery } from '../../api/queries/getDealersOptions';
import { CompanyTheme, type DealerFilteringRule } from '../../api/types';
import { useThemeComponents } from '../../themes/hooks';
import PorscheV3SelectField from '../../themes/porscheV3/Fields/SelectField';
import useTranslatedString from '../../utilities/useTranslatedString';
import { useCompany } from '../contexts/CompanyContextManager';
import SelectField, { type SelectFieldProps } from './SelectField';

export type DealerSelectFieldProps = Omit<SelectFieldProps, 'children' | 'options'> & {
    dealers?: DealerOptionsDataFragment[];
    companyId?: string;
    forceSkipFetch?: boolean;
    addNoneOption?: boolean;
    productionOnly?: boolean;
    checkConfiguratorStock?: boolean;
    customComponent?: React.ComponentType<SelectFieldProps>;
    isTranslatedOption?: boolean;
};

const filterOption: SelectProps['filterOption'] = (inputValue, option) =>
    option.label.toString().toLowerCase().includes(inputValue.toLowerCase());

const DealerSelectField = ({
    dealers: dealersFromProps,
    companyId,
    forceSkipFetch = false,
    addNoneOption = false,
    productionOnly = false,
    checkConfiguratorStock = false,
    customComponent: CustomComponent,
    ...props
}: DealerSelectFieldProps) => {
    const { dealers, options, loading } = useDealerSelectFieldOptions({
        companyId,
        dealers: dealersFromProps,
        forceSkipFetch,
        addNoneOption,
        productionOnly,
        checkConfiguratorStock,
        isTranslatedOption: props.isTranslatedOption,
    });

    const { theme } = useThemeComponents();

    const DefaultSelectField = theme === CompanyTheme.PorscheV3 ? PorscheV3SelectField : SelectField;

    const UsedSelectFieldComponent = useMemo(
        () => CustomComponent ?? DefaultSelectField,
        [CustomComponent, DefaultSelectField]
    );

    return (
        <UsedSelectFieldComponent
            {...props}
            filterOption={filterOption}
            loading={!dealers && loading}
            options={options}
        />
    );
};

export default DealerSelectField;

export type UseDealerSelectFieldOptionsArgs = {
    companyId?: string;
    dealers?: DealerOptionsDataFragment[];
    forceSkipFetch?: boolean;
    addNoneOption?: boolean;
    productionOnly?: boolean;
    checkConfiguratorStock?: boolean;
    isTranslatedOption?: boolean;
};

export const useDealerSelectFieldOptions = ({
    companyId,
    dealers: dealersFromProps,
    forceSkipFetch = false,
    addNoneOption = false,
    productionOnly = false,
    checkConfiguratorStock = false,
    isTranslatedOption = false,
}: UseDealerSelectFieldOptionsArgs) => {
    const translatedString = useTranslatedString();

    const filter = useMemo(
        (): DealerFilteringRule => ({ companyId, productionOnly, checkConfiguratorStock }),
        [companyId, productionOnly, checkConfiguratorStock]
    );
    const { t } = useTranslation('moduleDetails');
    const company = useCompany(true);

    const { data, loading } = useGetDealersOptionsQuery({
        fetchPolicy: 'cache-and-network',
        variables: { filter: { ...filter, companyId: companyId || company?.id } },
        skip: !!dealersFromProps || forceSkipFetch,
    });

    const dealers = useMemo(() => dealersFromProps || data?.dealers || [], [data?.dealers, dealersFromProps]);

    const options = useMemo(
        () =>
            [
                addNoneOption && { value: null, label: t('moduleDetails:selectFields.options.none') },
                ...dealers.map(dealer => ({
                    value: dealer.id,
                    label: isTranslatedOption ? translatedString(dealer.legalName) : dealer.displayName,
                })),
            ].filter(Boolean),
        [addNoneOption, dealers, isTranslatedOption, t, translatedString]
    );

    return { dealers, options, loading };
};
