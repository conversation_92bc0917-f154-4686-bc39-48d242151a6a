import { Input } from 'antd';
import { TextAreaProps } from 'antd/es/input';
import { useField } from 'formik';
import { trim } from 'lodash/fp';
import { memo, ReactNode, useCallback } from 'react';
import { useRefineContentButton } from '../button/RefineContentButton';
import { type ContentRefinementProps } from '../button/RefineContentButtonProps';
import FormItem, { FormItemProps } from './FormItem';

export interface TextAreaFieldProps extends Omit<TextAreaProps, 'value' | 'onChange'>, ContentRefinementProps {
    name: string;
    label?: JSX.Element | string;
    tooltip?: FormItemProps['tooltip'];
    trimOnBlur?: boolean;
}

type TextAreaFieldWithAddOnProps = TextAreaFieldProps & { addMoreButton?: ReactNode };

const TextAreaField = ({
    name,
    required,
    label,
    addMoreButton,
    tooltip,
    withContentRefinement,
    trimOnBlur = true,
    ...props
}: TextAreaFieldWithAddOnProps) => {
    const [field, meta, helper] = useField({ name });
    const refineContentButton = useRefineContentButton();

    // Handle trimming on blur for KYC fields
    const { onBlur: propsOnBlur } = props;
    const handleBlur = useCallback(
        (event: React.FocusEvent<HTMLTextAreaElement>) => {
            // Call the original onBlur first
            if (propsOnBlur) {
                propsOnBlur(event);
            } else {
                field.onBlur(event);
            }

            // Apply trimming if enabled
            if (trimOnBlur && typeof field.value === 'string') {
                const trimmedValue = trim(field.value);
                if (trimmedValue !== field.value) {
                    helper.setValue(trimmedValue);
                }
            }
        },
        [propsOnBlur, field, trimOnBlur, helper]
    );

    return (
        <FormItem label={label} meta={meta} required={required} tooltip={tooltip}>
            <div>
                <Input.Group>
                    <Input.TextArea
                        autoSize={{ minRows: 2, maxRows: 6 }}
                        // spread props
                        {...props}
                        // then spread the field properties itself
                        {...field}
                        onBlur={handleBlur}
                    />
                    {addMoreButton && <span className="ant-input-group-addon">{addMoreButton}</span>}
                </Input.Group>
                {withContentRefinement && refineContentButton.render({ name })}
            </div>
        </FormItem>
    );
};

export default memo(TextAreaField);
