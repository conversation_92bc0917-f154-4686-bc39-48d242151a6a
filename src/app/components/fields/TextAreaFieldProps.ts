import { TextAreaProps } from 'antd/es/input';
import { type ContentRefinementProps } from '../button/RefineContentButtonProps';
import { FormItemProps } from './FormItem';

export interface TextAreaFieldProps extends Omit<TextAreaProps, 'value' | 'onChange'>, ContentRefinementProps {
    name: string;
    label?: JSX.Element | string;
    tooltip?: FormItemProps['tooltip'];
    trimOnBlur?: boolean;
}
