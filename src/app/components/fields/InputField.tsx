import { Input, InputProps } from 'antd';
import { useField } from 'formik';
import { trim } from 'lodash/fp';
import { memo, useCallback, useEffect, useMemo } from 'react';
import styled, { css } from 'styled-components';
import FormItem, { FormItemProps } from './FormItem';

export const adminCss = (showCount?: Pick<InputProps, 'showCount'>, disabled?: Pick<InputProps, 'disabled'>) => css`
    &.ant-input-affix-wrapper,
    .ant-input-affix-wrapper-focused {
        box-shadow: none;
        border-radius: 0;
    }

    &.ant-input,
    input.ant-input {
        background-color: transparent;
        box-shadow: none;
        border-radius: 2px;
        font-size: var(--input-font-size, 16px);
        &.ant-input:focus,
        .ant-input-focused {
            border-color: var(--ant-primary-color);
            box-shadow: none;
            & + .ant-input-group-addon {
                border-color: var(--ant-primary-color);
            }
        }
        &.ant-input:not(.ant-input-disabled):hover + .ant-input-group-addon {
            border-color: var(--ant-primary-5);
        }
        &.ant-input-status-error:not(.ant-input-disabled):not(.ant-input-borderless).ant-input:focus {
            box-shadow: none;
        }
    }
    & .ant-input-group-addon {
        border-right: none;
        background-color: transparent;
        & > .ant-btn {
            color: var(--ant-primary-color);
        }
    }
`;

export const StyledInput = styled(Input)<{
    showCount?: Pick<InputProps, 'showCount'>;
    disabled?: Pick<InputProps, 'disabled'>;
}>`
    ${({ showCount, disabled }) => adminCss(showCount, disabled)}
`;

export interface InputFieldProps extends Omit<InputProps, 'value' | 'onChange' | 'autoComplete'> {
    name: string;
    label?: React.ReactNode;
    itemProps?: Omit<FormItemProps, 'label' | 'meta' | 'required' | 'children'>;
    tooltip?: FormItemProps['tooltip'];
    isTableInput?: boolean;
    removeWhiteSpace?: boolean;
    applyDefaultStyles?: boolean;
    trimOnBlur?: boolean;
}

const InputField = ({
    name,
    required,
    label,
    itemProps,
    tooltip,
    isTableInput = false,
    applyDefaultStyles = true,
    removeWhiteSpace,
    trimOnBlur = true,
    ...props
}: InputFieldProps) => {
    const [field, meta, helper] = useField({ name });

    useEffect(() => {
        if (removeWhiteSpace && typeof field.value === 'string') {
            helper.setValue(field.value.replace(/\s/g, ''));
        }
    }, [removeWhiteSpace, field.value, helper]);

    // Handle trimming on blur for KYC fields (unless they have removeWhiteSpace)
    const { onBlur: propsOnBlur } = props;
    const handleBlur = useCallback(
        (event: React.FocusEvent<HTMLInputElement>) => {
            // Call the original onBlur first
            if (propsOnBlur) {
                propsOnBlur(event);
            } else {
                field.onBlur(event);
            }

            // Apply trimming if enabled and not using removeWhiteSpace
            if (trimOnBlur && !removeWhiteSpace && typeof field.value === 'string') {
                const trimmedValue = trim(field.value);
                if (trimmedValue !== field.value) {
                    helper.setValue(trimmedValue);
                }
            }
        },
        [propsOnBlur, field.onBlur, field.value, trimOnBlur, removeWhiteSpace, helper]
    );

    const EnhancedInput = useMemo(() => (applyDefaultStyles ? StyledInput : Input), [applyDefaultStyles]);

    return (
        <FormItem
            {...itemProps}
            isTableInput={isTableInput}
            label={label}
            meta={meta}
            required={required}
            tooltip={tooltip}
        >
            <EnhancedInput
                autoComplete="off"
                // spread props
                {...props}
                // then spread the field properties itself
                {...field}
                onBlur={handleBlur}
                {...(applyDefaultStyles && { showCount: props.showCount })}
            />
        </FormItem>
    );
};

export default memo(InputField);
