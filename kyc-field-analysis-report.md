# Comprehensive KYC Field Analysis Report

## Executive Summary

This analysis covers **108 distinct KYC fields** across the customer onboarding system, organized into 8 major categories. The current validation system has significant gaps in whitespace handling, with only **1 existing validator** (`requiredNonEmptyString`) that properly handles whitespace-only inputs. Most fields currently use basic `requiredString` validation that accepts whitespace-only values, creating data quality vulnerabilities.

### Key Findings:
- **Critical Gap**: 95% of string fields lack whitespace-only input validation
- **Inconsistent Trimming**: Only 13 fields have automatic whitespace removal
- **Validation Fragmentation**: Multiple validation approaches across similar field types
- **Missing Sanitization**: No centralized input sanitization utilities

---

## 1. Field Inventory & Classification

### 1.1 Personal Information Fields (18 fields)
| Field Name | Data Type | Mandatory Status | Current Validation | Whitespace Handling |
|------------|-----------|------------------|-------------------|-------------------|
| `Title` | String (Select) | Conditional | requiredString | None |
| `NonBinaryTitle` | String (Select) | Conditional | requiredString | None |
| `Salutation` | String (Select) | Conditional | requiredString | None |
| `SalutationBmw` | String (Select) | Conditional | requiredString | None |
| `LastNameFront` | String (Input) | Conditional | requiredString | None |
| `FirstName` | String (Input) | Conditional | requiredString | None |
| `LastName` | String (Input) | Conditional | requiredString | None |
| `LastNameJapan` | String (Input) | Conditional | requiredString | None |
| `FirstNameJapan` | String (Input) | Conditional | requiredString | None |
| `FullName` | String (Input) | Conditional | requiredString | None |
| `Email` | String (Input) | Conditional | requiredString + validEmail | **Trimmed** |
| `Phone` | Phone Object | Conditional | validPhone + verifiedPhone | **Trimmed** |
| `Birthday` | Date | Conditional | validDateOfBirthKYC | N/A |
| `Nationality` | String (Select) | Conditional | requiredString | None |
| `Citizenship` | String (Select) | Conditional | requiredString | None |
| `Race` | String (Select) | Conditional | requiredString | None |
| `Gender` | String (Select) | Conditional | requiredString | None |
| `MaritalStatus` | String (Select) | Conditional | requiredString | None |

### 1.2 Address Fields (15 fields)
| Field Name | Data Type | Mandatory Status | Current Validation | Whitespace Handling |
|------------|-----------|------------------|-------------------|-------------------|
| `PostalCode` | String (Input) | Conditional | validPostalCode | **Trimmed** |
| `Address` | String (Input) | Conditional | requiredString | None |
| `UnitNumber` | String (Input) | Optional | requiredString | None |
| `District` | String (Input) | Conditional | requiredString | None |
| `Road` | String (Input) | Conditional | requiredString | None |
| `Region` | String (Select) | Conditional | requiredString | None |
| `City` | String (Input) | Conditional | requiredString | None |
| `Country` | String (Select) | Conditional | requiredString | None |
| `TimeOfAddress` | Number | Conditional | requiredNumber | N/A |
| `ResidentialStatus` | String (Select) | Conditional | requiredString | None |
| `Telephone` | Phone Object | Conditional | validPhone | **Trimmed** |
| `CorrespondenceCity` | String (Input) | Conditional | requiredString | None |
| `CorrespondenceDistrict` | String (Input) | Conditional | requiredString | None |
| `CorrespondenceAddress` | String (Input) | Conditional | requiredString | None |
| `Emirate` | String (Select) | Conditional | requiredString | None |

### 1.3 Employment Fields (12 fields)
| Field Name | Data Type | Mandatory Status | Current Validation | Whitespace Handling |
|------------|-----------|------------------|-------------------|-------------------|
| `JobTitle` | String (Select) | Conditional | requiredString | None |
| `JobTitleTh` | String (Select) | Conditional | requiredString | None |
| `Occupation` | String (Input) | Conditional | requiredString | None |
| `EmploymentStatus` | String (Select) | Conditional | requiredString | None |
| `TimeOfEmployment` | Number | Conditional | requiredNumber | N/A |
| `CompanyName` | String (Input) | Conditional | requiredString | None |
| `CompanyCity` | String (Input) | Conditional | requiredString | None |
| `CompanyDistrict` | String (Input) | Conditional | requiredString | None |
| `CompanyAddress` | String (Input) | Conditional | requiredString | None |
| `CompanyPhone` | Phone Object | Conditional | validPhone | **Trimmed** |
| `CompanyPhoneExtension` | String (Input) | Conditional | requiredString | **Trimmed** |
| `BusinessTitle` | String (Input) | Conditional | requiredString | None |

### 1.4 Identity & Document Fields (8 fields)
| Field Name | Data Type | Mandatory Status | Current Validation | Whitespace Handling |
|------------|-----------|------------------|-------------------|-------------------|
| `IdentityNumber` | String (Input) | Conditional | validIdentity | **Trimmed** |
| `DrivingLicense` | Array | Conditional | Custom validation | None |
| `DrivingLicenseTh` | Array | Conditional | Custom validation | None |
| `DrivingLicenseMy` | Array | Conditional | Custom validation | None |
| `UAEDrivingLicense` | Array | Conditional | Custom validation | None |
| `Passport` | String (Input) | Conditional | requiredString | None |
| `Education` | String (Select) | Conditional | requiredString | None |
| `UAEIdentitySet` | Object | Conditional | Custom validation | None |

### 1.5 Financial Fields (6 fields)
| Field Name | Data Type | Mandatory Status | Current Validation | Whitespace Handling |
|------------|-----------|------------------|-------------------|-------------------|
| `MonthlyIncome` | Number | Conditional | requiredNumber | N/A |
| `OtherIncome` | Number | Conditional | requiredNumber | N/A |
| `IncomeType` | String (Select) | Conditional | requiredString | None |
| `DateOfJoining` | Date | Conditional | Custom validation | N/A |
| `PreferredFirstPaymentDate` | Date | Conditional | Custom validation | N/A |
| `SalaryTransferredBankSet` | Object | Conditional | Custom validation | None |

### 1.6 Corporate Fields (8 fields)
| Field Name | Data Type | Mandatory Status | Current Validation | Whitespace Handling |
|------------|-----------|------------------|-------------------|-------------------|
| `CorporateName` | String (Input) | Conditional | requiredString | None |
| `CorporateIdentityNumber` | String (Input) | Conditional | requiredString | None |
| `CorporateRegistrationDate` | Date | Conditional | Custom validation | N/A |
| `CorporateIndustryCategory` | String (Select) | Conditional | requiredString | None |
| `CorporateRegisteredCapital` | Number | Conditional | requiredNumber | N/A |
| `CorporateAnnualRevenue` | Number | Conditional | requiredNumber | N/A |
| `CorporateNumberOfEmployee` | Number | Conditional | requiredNumber | N/A |
| `CorporatePhone` | Phone Object | Conditional | validPhone | **Trimmed** |

### 1.7 Upload Fields (4 fields)
| Field Name | Data Type | Mandatory Status | Current Validation | Whitespace Handling |
|------------|-----------|------------------|-------------------|-------------------|
| `UploadIdentity` | File Array | Conditional | requiredUploadFile | N/A |
| `UploadDrivingLicense` | File Array | Conditional | requiredUploadFile | N/A |
| `UploadPassport` | File Array | Conditional | requiredUploadFile | N/A |
| `UploadOtherDocument` | File Array | Conditional | requiredUploadFile | N/A |

### 1.8 Miscellaneous Fields (37 fields)
Including delivery addresses, reference details, vehicle information, and other specialized fields.

---

## 2. Current Validation Analysis

### 2.1 Validation Patterns Identified

#### Pattern 1: Basic Required String (Most Common - 65+ fields)
```typescript
validators.requiredString(`${prefixName}.value`)
```
**Issue**: Accepts whitespace-only inputs like "   " or "\t\t"

#### Pattern 2: Email Validation (1 field)
```typescript
validators.compose(
    validators.only(() => kyc.isRequired, validators.requiredString(`${prefixName}.value`)),
    validators.only(values => hasValue(`${prefixName}.value`)(values), validators.validEmail(`${prefixName}.value`))
)
```
**Status**: Has trimming but still vulnerable to whitespace-only inputs

#### Pattern 3: Phone Validation (5 fields)
```typescript
validators.compose(
    validators.requiredNumber(`${prefixName}.value.prefix`),
    validators.validPhone(`${prefixName}.value.value`, `${prefixName}.value.prefix`)
)
```
**Status**: Has trimming and format validation

#### Pattern 4: Identity Validation (1 field)
```typescript
validators.validIdentity(citizenshipField, nricField, dobField, nationalityField, isRequired, defaultNationality, bankProvider)
```
**Status**: Complex validation with trimming

#### Pattern 5: Existing Whitespace-Safe Validation (1 field - PASSWORD only)
```typescript
const requiredNonEmptyString = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const input = trim(value);
        if (whiteSpace.test(input) || empty.test(input)) {
            return context.defaultMessages.requiredValue;
        }
        return null;
    });
```
**Status**: Properly rejects whitespace-only inputs

### 2.2 Validation Gaps

1. **Whitespace-Only Acceptance**: 95% of string fields accept "   " as valid input
2. **Inconsistent Trimming**: Only 13/108 fields have automatic whitespace removal
3. **No Centralized Validation**: Each field type has custom validation logic
4. **Missing Edge Case Handling**: No validation for mixed whitespace scenarios

---

## 3. Field-Specific Requirements Documentation

### 3.1 Name Fields Requirements
**Fields**: `FirstName`, `LastName`, `FullName`, `LastNameJapan`, `FirstNameJapan`, `LastNameFront`

**Character Support**:
- Unicode characters for international names (✓ Current: Basic support)
- Hyphens, apostrophes, periods (✓ Current: Allowed)
- Minimum 1 character after trimming (✗ Current: Missing)

**Validation Needs**:
- Reject whitespace-only inputs
- Support Unicode regex: `/^[\p{L}\p{M}\s\-'\.]+$/u`
- Length constraints: 1-100 characters

### 3.2 Address Fields Requirements
**Fields**: `Address`, `City`, `District`, `Road`, `UnitNumber`, `CorrespondenceAddress`

**Character Support**:
- Alphanumeric characters, spaces, common punctuation
- Building numbers, unit designations
- International address formats

**Validation Needs**:
- Reject whitespace-only inputs
- Support regex: `/^[A-Za-z0-9\s\-\.\,\#\/]+$/`
- Length constraints: 1-255 characters

### 3.3 Numeric Fields Requirements
**Fields**: `Phone`, `PostalCode`, `IdentityNumber`, `CorporatePhone`

**Format Support**:
- Country-specific formatting
- Remove non-numeric characters where appropriate
- Validate proper number formatting

**Validation Needs**:
- Reject whitespace-only inputs
- Format-specific validation
- Length constraints based on country

### 3.4 Corporate Fields Requirements
**Fields**: `CorporateName`, `CorporateIdentityNumber`, `CompanyName`

**Character Support**:
- Business name characters
- Registration number formats
- International business naming conventions

**Validation Needs**:
- Reject whitespace-only inputs
- Business-specific character sets
- Length constraints: 1-200 characters

---

## 4. Input Examples & Edge Cases

### 4.1 Valid Input Examples

#### Name Fields:
- `"John"` ✓
- `"Mary-Jane"` ✓
- `"O'Connor"` ✓
- `"José María"` ✓ (Unicode)
- `"李小明"` ✓ (Chinese characters)

#### Address Fields:
- `"123 Main Street"` ✓
- `"Apt 4B, Building C"` ✓
- `"Unit #12-34"` ✓
- `"東京都渋谷区"` ✓ (Japanese)

#### Corporate Fields:
- `"ABC Corp Ltd."` ✓
- `"Smith & Associates"` ✓
- `"Tech Solutions Inc."` ✓

### 4.2 Invalid Input Examples

#### Whitespace-Only Inputs (Currently Accepted - ISSUE):
- `"   "` ✗ (spaces only)
- `"\t\t"` ✗ (tabs only)
- `"\n\n"` ✗ (newlines only)
- `" \t \n "` ✗ (mixed whitespace)

#### Empty Inputs:
- `""` ✗ (for required fields)
- `null` ✗ (for required fields)
- `undefined` ✗ (for required fields)

#### Invalid Characters:
- `"John<script>"` ✗ (HTML/script injection)
- `"Name@#$%"` ✗ (invalid special characters for names)
- `"123!@#"` ✗ (invalid characters for addresses)

### 4.3 Edge Cases for Whitespace Validation

#### Leading/Trailing Whitespace (Should be trimmed):
- `" John "` → `"John"` ✓
- `"\tMary\n"` → `"Mary"` ✓

#### Internal Whitespace (Should be preserved):
- `"Mary Jane"` ✓ (legitimate space)
- `"New York"` ✓ (legitimate space)

#### Single Character Inputs:
- `"J"` ✓ (valid middle initial)
- `"李"` ✓ (valid single Chinese character)
- `" "` ✗ (single space - should be rejected)

---

## 5. Usage Context & Dependencies

### 5.1 Form Workflow Dependencies

#### Customer Onboarding Flow:
1. **Personal Details** → Address Details → Employment Details
2. **Conditional Requirements**: Some fields become mandatory based on others
3. **Country-Specific Rules**: Different validation rules per country

#### Field Dependencies:
- `Passport` required only if `Citizenship` = "Others"
- `IdentityNumber` required only if `Citizenship` = "Singaporean/PR"
- Name fields: Either `FullName` OR (`FirstName` + `LastName`) required

### 5.2 External Service Integration

#### MyInfo Integration (Singapore):
- Pre-fills customer data from government database
- Requires validation consistency with MyInfo data format
- Fields: `Address`, `PostalCode`, `IdentityNumber`, `Birthday`

#### Bank Integration:
- Customer data sent to bank APIs for verification
- Requires clean, validated data
- Critical fields: `FullName`, `IdentityNumber`, `Email`, `Phone`

#### CAP Integration:
- Customer data synchronized with dealer systems
- Requires consistent formatting
- All customer fields potentially affected

### 5.3 Mobile Verification Dependencies

#### Phone Verification Flow:
- `Phone` field triggers OTP verification when `mobileVerification` enabled
- Requires valid phone format before OTP can be sent
- Verification status affects form submission

---

## 6. Validation Gap Analysis

### 6.1 Critical Issues

#### Issue 1: Whitespace-Only Input Acceptance
- **Severity**: High
- **Affected Fields**: 95+ string fields
- **Impact**: Data quality issues, potential security vulnerabilities
- **Current State**: Only `PASSWORD` field properly validates

#### Issue 2: Inconsistent Trimming Logic
- **Severity**: Medium
- **Affected Fields**: 95+ string fields
- **Impact**: Inconsistent data storage, user experience issues
- **Current State**: Only 13 fields have trimming

#### Issue 3: Fragmented Validation Approaches
- **Severity**: Medium
- **Affected Fields**: All fields
- **Impact**: Maintenance complexity, inconsistent behavior
- **Current State**: Multiple validation patterns without centralization

#### Issue 4: Missing Input Sanitization
- **Severity**: Medium
- **Affected Fields**: All string fields
- **Impact**: Potential XSS vulnerabilities, data corruption
- **Current State**: No centralized sanitization utilities

### 6.2 Validation Inconsistencies

#### Name Field Inconsistencies:
- `FirstName` and `LastName` use basic `requiredString`
- `FullName` uses same validation but different business logic
- No Unicode character validation for international names

#### Address Field Inconsistencies:
- `PostalCode` has format validation and trimming
- `Address`, `City`, `District` use basic `requiredString`
- No consistent character set validation

#### Phone Field Inconsistencies:
- `Phone` has comprehensive validation with trimming
- `Telephone`, `CompanyPhone` have similar but slightly different validation
- Inconsistent error handling across phone fields

---

## 7. Field Requirements Matrix

### 7.1 String Field Validation Requirements

| Field Category | Min Length | Max Length | Character Set | Trimming | Whitespace Validation |
|----------------|------------|------------|---------------|----------|---------------------|
| **Name Fields** | 1 | 100 | Unicode + `-'\.` | ✓ | ✓ Required |
| **Address Fields** | 1 | 255 | Alphanumeric + spaces + punctuation | ✓ | ✓ Required |
| **Company Fields** | 1 | 200 | Business characters | ✓ | ✓ Required |
| **ID Fields** | 1 | 50 | Alphanumeric | ✓ | ✓ Required |
| **Email Fields** | 5 | 255 | Email format | ✓ | ✓ Required |

### 7.2 Numeric Field Validation Requirements

| Field Category | Data Type | Format Validation | Range Validation | Trimming |
|----------------|-----------|-------------------|------------------|----------|
| **Phone Fields** | Phone Object | Country-specific | N/A | ✓ |
| **Income Fields** | Number | Currency format | > 0 | N/A |
| **Date Fields** | Date | ISO format | Age constraints | N/A |
| **Postal Codes** | String | Country-specific | N/A | ✓ |

### 7.3 Conditional Validation Requirements

| Field | Condition | Validation Rule |
|-------|-----------|----------------|
| `Passport` | `Citizenship` = "Others" | Required + format validation |
| `IdentityNumber` | `Citizenship` = "Singaporean/PR" | Required + NRIC validation |
| `CompanyName` | `EmploymentStatus` = "Employed" | Required + business name validation |
| `MonthlyIncome` | `EmploymentStatus` = "Employed" | Required + positive number |

---

## 9. Technical Implementation Notes

### 9.1 Existing Validation Infrastructure

#### Current Validator Structure:
```typescript
// Located in: src/app/utilities/validators.ts
const requiredNonEmptyString = (field: PropertyPath) =>
    validators.custom(field, (value, values, errors, context) => {
        const input = trim(value);
        if (whiteSpace.test(input) || empty.test(input)) {
            return context.defaultMessages.requiredValue;
        }
        return null;
    });
```

#### Current Trimming Implementation:
```typescript
// Located in: src/app/utilities/kycPresets/index.tsx
export const fieldsToRemoveWhitespace = [
    LocalCustomerFieldKey.Email,
    LocalCustomerFieldKey.IdentityNumber,
    LocalCustomerFieldKey.Phone,
    // ... 13 fields total
];
```

### 9.2 Integration Points

#### Form Components:
- `InputField` components need trimming logic updates
- `PhoneField` components already have trimming
- `SelectField` components don't need whitespace validation

#### Validation Hooks:
- `useKYCValidators` needs enhancement for whitespace validation
- `useProceedWithCustomerValidators` needs updates
- Server-side validation in `ApplicantKYCStep.ts` needs alignment

#### Error Handling:
- New error codes needed for whitespace validation
- Translation keys need addition to locale files
- Error display components already support new error types

---

## 10. Conclusion

The KYC field validation system requires comprehensive enhancement to address critical whitespace handling gaps. With 108 fields across 8 categories, the implementation of centralized whitespace validation will significantly improve data quality and security. The existing infrastructure provides a solid foundation for these improvements, with clear patterns for validation, trimming, and error handling already established.
