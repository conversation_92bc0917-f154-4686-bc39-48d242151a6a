# KYC Field Validation System Documentation

## Overview

This document describes the enhanced KYC field validation system that ensures data quality by rejecting whitespace-only inputs while maintaining support for optional fields and field-specific validation rules.

## Core Principles

1. **Mandatory fields** must contain valid, non-whitespace content
2. **Optional fields** can be empty OR contain valid, non-whitespace content
3. **Field-specific validation** applies after whitespace validation
4. **Consistent error messaging** guides users appropriately
5. **International character support** for global accessibility

## Centralized Validation Architecture

### Base Validation Function

```typescript
validateKYCField(field: PropertyPath, options: {
    isRequired?: boolean;
    minLength?: number;
    maxLength?: number;
    customValidator?: (value: string) => boolean;
    errorMessage?: string;
})
```

**Parameters:**
- `isRequired`: Whether the field is mandatory (default: true)
- `minLength`: Minimum character length (default: 1)
- `maxLength`: Maximum character length (default: 100)
- `customValidator`: Field-specific validation function
- `errorMessage`: Custom error message for validation failures

**Validation Logic:**
1. Handle empty values for optional fields (return null if not required and empty)
2. Trim input and reject whitespace-only content
3. Check length constraints
4. Apply custom field-specific validation
5. Return appropriate error messages

## Field-Specific Validators

### Name Fields (`validKYCName`)

**Purpose**: Validate person names with international character support

**Regex Pattern**: `/^[A-Za-zÀ-ÖØ-öø-ÿ\u0100-\u017F\u0180-\u024F\u1E00-\u1EFF\u0400-\u04FF\u4E00-\u9FFF\u3040-\u309F\u30A0-\u30FF\u0600-\u06FF\s\-'\.]+$/`

**Supported Characters**:
- Latin letters (A-Z, a-z)
- Extended Latin characters (À-ÖØ-öø-ÿ)
- Unicode ranges for international scripts:
  - Latin Extended-A and B
  - Latin Extended Additional
  - Cyrillic (Russian, Bulgarian, etc.)
  - CJK Unified Ideographs (Chinese, Japanese, Korean)
  - Hiragana and Katakana (Japanese)
  - Arabic
- Spaces, hyphens, apostrophes, periods

**Examples**:
- ✅ Valid: "John", "Mary-Jane", "O'Connor", "José María", "李小明", "محمد"
- ❌ Invalid: "John123", "John@Smith", "   " (whitespace-only)

**Fields Using This Validator**:
- FirstName, LastName, FullName
- LastNameJapan, FirstNameJapan, LastNameFront

### Address Fields (`validKYCAddress`)

**Purpose**: Validate address components with appropriate formatting

**Regex Pattern**: `/^[A-Za-z0-9\s\-\.\,\#\/]+$/`

**Supported Characters**:
- Letters and numbers
- Spaces, hyphens, periods, commas
- Hash (#) for unit numbers
- Forward slash (/) for complex addresses

**Examples**:
- ✅ Valid: "123 Main Street", "Apt 4B, Building C", "Unit #12-34", "123/45 Road Name"
- ❌ Invalid: "123 Main St @", "   " (whitespace-only)

**Fields Using This Validator**:
- Address, City, District, Road, UnitNumber
- CompanyAddress, CompanyCity, CompanyDistrict
- CorrespondenceAddress, CorrespondenceCity, CorrespondenceDistrict
- DeliveryAddress

### Business Name Fields (`validKYCBusinessName`)

**Purpose**: Validate company and business names

**Regex Pattern**: `/^[A-Za-z0-9\s\-\.\,\#\/\&\(\)]+$/`

**Supported Characters**:
- All address characters plus:
- Ampersand (&) for "Company & Associates"
- Parentheses () for "(Pvt) Ltd"

**Examples**:
- ✅ Valid: "ABC Corp Ltd.", "Smith & Associates", "Tech Solutions (Pvt) Ltd"
- ❌ Invalid: "<EMAIL>", "   " (whitespace-only)

**Fields Using This Validator**:
- CompanyName, CorporateName
- BusinessTitle, Occupation

### Alphanumeric Fields (`validKYCAlphanumeric`)

**Purpose**: Validate general alphanumeric content

**Regex Pattern**: `/^[A-Za-z0-9\s\-]+$/`

**Supported Characters**:
- Letters, numbers, spaces, hyphens

**Examples**:
- ✅ Valid: "ABC123", "Test-Value", "Value 123"
- ❌ Invalid: "ABC@123", "   " (whitespace-only)

**Fields Using This Validator**:
- Title, Salutation, Nationality, Country
- Citizenship, Race, Gender, MaritalStatus
- ResidentialStatus, Region, EmploymentStatus
- Education, Emirate, IncomeType, etc.

### General String Fields (`validKYCString`)

**Purpose**: Basic string validation without format restrictions

**Validation**: Only rejects whitespace-only inputs

**Examples**:
- ✅ Valid: Any non-whitespace string
- ❌ Invalid: "   ", "\t\t", "" (whitespace-only or empty)

**Fields Using This Validator**:
- CompanyPhoneticName, CompanyPhoneExtension
- PurchaseIntention, Comments

### Numeric Fields (`validKYCNumeric`)

**Purpose**: Validate numeric inputs with range constraints

**Validation Logic**:
1. Check if value is a valid number
2. Verify value is within specified range
3. Handle optional fields appropriately

**Examples**:
- ✅ Valid: 123, 45.67, 0
- ❌ Invalid: "abc", "", "   " (for required fields)

**Fields Using This Validator**:
- MonthlyIncome, OtherIncome (range: 0-999,999,999)
- TimeOfAddress, TimeOfEmployment (range: 0-100)
- CorporateRegisteredCapital, CorporateAnnualRevenue (range: 0-999,999,999)

## Optional Field Handling

### Implementation Pattern

```typescript
// For required fields
validators.validKYCName(`${prefixName}.value`, true)

// For optional fields
validators.validKYCName(`${prefixName}.value`, false)

// Dynamic based on KYC configuration
validators.validKYCName(`${prefixName}.value`, kyc.isRequired)
```

### Validation Behavior

**Required Fields (`isRequired: true`)**:
- Empty input → Error: "This field is required"
- Whitespace-only → Error: "This field is required"
- Valid content → Pass validation

**Optional Fields (`isRequired: false`)**:
- Empty input → Pass validation (null)
- Whitespace-only → Error: "This field is required"
- Valid content → Pass validation

## Error Messages

### Standard Error Codes

```json
{
    "formErrors": {
        "requiredValue": "This field is required",
        "whitespaceOnlyInput": "This field cannot contain only spaces",
        "invalidNameFormat": "Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed",
        "invalidAddressFormat": "Invalid address format. Only letters, numbers, spaces, and common punctuation are allowed",
        "invalidBusinessNameFormat": "Invalid business name format",
        "invalidAlphanumericFormat": "Invalid format. Only letters, numbers, spaces, and hyphens are allowed",
        "invalidFormat": "Invalid format",
        "minLength": "Minimum {{minLength}} characters required",
        "maxLength": "Maximum {{maxLength}} characters allowed",
        "invalidNumber": "Invalid number format",
        "numberRange": "Number must be between {{minValue}} and {{maxValue}}"
    }
}
```

## Input Trimming

### Real-time Space Removal vs. Validation-time Trimming

**IMPORTANT**: There are two different types of whitespace handling in the system:

1. **Real-time space removal** (`removeWhiteSpace` prop) - Removes spaces immediately as you type
2. **Validation-time trimming** - Trims leading/trailing spaces only during form validation

### Fields with Real-time Space Removal

Only fields where spaces are NEVER valid have real-time space removal:

```typescript
export const fieldsToRemoveWhitespace = [
    // Only include fields where spaces should be completely removed in real-time
    // These are typically fields where spaces are never valid (IDs, phone numbers, etc.)
    LocalCustomerFieldKey.Email,
    LocalCustomerFieldKey.IdentityNumber,
    LocalCustomerFieldKey.Phone,
    LocalCustomerFieldKey.Telephone,
    LocalCustomerFieldKey.CompanyPhone,
    LocalCustomerFieldKey.CorporatePhone,
    LocalCustomerFieldKey.CompanyPhoneExtension,
    LocalCustomerFieldKey.PostalCode,
    LocalCustomerFieldKey.CurrentVehicleRegistrationNumber,
    LocalCustomerFieldKey.CurrentVehicleVin,
    LocalCustomerFieldKey.CorporateIdentityNumber,
    LocalCustomerFieldKey.Passport, // Document IDs should not have spaces

    // NOTE: We do NOT include name fields, address fields, or other text fields here
    // because they legitimately need spaces. Our validation handles whitespace-only
    // inputs at form submission time, not during real-time input.
];
```

### Fields with Normal Typing Behavior

**Name fields, address fields, and other text fields** allow normal typing with spaces:
- Users can type spaces normally
- Validation only occurs on form submission
- Whitespace-only inputs are rejected during validation
- Leading/trailing spaces are trimmed during validation

### Trimming Behavior

1. **Real-time removal**: Spaces removed immediately for ID/phone fields
2. **Validation-time trimming**: Leading/trailing spaces trimmed for all fields during validation
3. **Internal spaces preserved**: Legitimate spaces within content are kept

## Testing Guidelines

### Unit Test Coverage

Each validator should be tested for:

1. **Valid inputs** (various legitimate formats)
2. **Whitespace-only inputs** (spaces, tabs, newlines)
3. **Empty inputs** (for optional field behavior)
4. **Invalid format inputs** (field-specific restrictions)
5. **Edge cases** (single characters, international characters)
6. **Length constraints** (minimum/maximum lengths)

### Integration Test Scenarios

1. **Form submission** with whitespace-only fields
2. **Optional field behavior** (empty vs. whitespace-only)
3. **Error message display** and user guidance
4. **Cross-browser compatibility** for Unicode support

## Migration Notes

### Backward Compatibility

- Existing valid data remains valid
- Enhanced validators are more permissive than restrictive
- Gradual rollout by field priority minimizes disruption

### Performance Considerations

- Regex patterns optimized for performance
- Validation functions execute in < 10ms
- No impact on form rendering performance

## Future Enhancements

### Potential Improvements

1. **Dynamic validation rules** based on country/region
2. **Real-time validation feedback** during typing
3. **Custom validation messages** per field type
4. **Validation rule configuration** through admin interface

### Extensibility

The centralized `validateKYCField` function can be extended with:
- Additional custom validators
- Dynamic length constraints
- Context-aware validation rules
- Integration with external validation services

## Conclusion

This enhanced KYC validation system provides robust data quality assurance while maintaining excellent user experience through clear error messages and proper handling of optional fields. The centralized architecture ensures consistency and maintainability across the entire application.
