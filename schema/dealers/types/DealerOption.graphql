type BasicDealerIntegrationDetails {
    """
    Dealer Code
    """
    dealerCode: String

    """
    Partner Number
    """
    partnerNumber: String

    """
    Assortment
    """
    assortment: String
}

type ListDealerOption {
    """
    ID
    """
    id: ObjectID!

    """
    Display Name
    """
    displayName: String!

    """
    Legal Name
    """
    legalName: TranslatedString!

    """
    Company Id
    """
    companyId: ObjectID!

    """
    PPSR Fee
    """
    ppsr: Float!

    """
    Dealer Establishment Fee
    """
    estFee: Float!

    """
    COE
    """
    coe: Float!

    """
    Dealer Integration Details
    """
    integrationDetails: BasicDealerIntegrationDetails!
}
