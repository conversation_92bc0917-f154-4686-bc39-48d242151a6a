# Summary

Enhance KYC field validation to reject whitespace-only inputs while maintaining data integrity and usability across all customer onboarding forms.

# Background

Market feedback indicates users can submit KYC forms with name fields containing only spaces (e.g., "   "), creating data quality issues and potential security vulnerabilities. This validation gap exists across all KYC fields, not just names.

# Requirements

## Core Validation Logic
1. **Mandatory fields**: Reject whitespace-only inputs (spaces, tabs, newlines)
2. **Optional fields**: If user provides input, validate it properly (no whitespace-only values)
3. **Input trimming**: Implement automatic trimming before validation (similar to existing PASSWORD field implementation)
4. **Preserve legitimate inputs**: Maintain support for valid single-character entries where appropriate

## Field-Specific Validation Rules (applied after whitespace validation)
1. **Name fields** (firstName, lastName, middleName):
   - Support Unicode characters for international names
   - Allow hyphens, apostrophes, periods (e.g., "<PERSON><PERSON>Connor", "<PERSON><PERSON><PERSON>", "Jr.")
   - Minimum 1 character after trimming
   
2. **Address fields** (street, city, state):
   - Support alphanumeric characters, spaces, common punctuation
   - Allow building numbers, unit designations
   
3. **Numeric fields** (phone, postal codes, ID numbers):
   - Validate proper number formatting
   - Remove non-numeric characters where appropriate
   
4. **Document/ID fields**:
   - Apply format-specific validation based on document type
   - Support alphanumeric combinations as required

## Implementation Requirements
1. **Centralized utility**: Create reusable validation functions in `src/server/utils/validation/` or similar location
2. **Consistent error handling**: Use existing error code patterns for translation
3. **Backward compatibility**: Ensure existing valid data remains valid
4. **Performance**: Validation should not impact form submission performance

# Technical Implementation

## Suggested Validation Utility Structure
```typescript
interface ValidationOptions {
  isRequired?: boolean;
  minLength?: number;
  maxLength?: number;
  customValidator?: (value: string) => boolean;
  allowWhitespace?: boolean; // for fields that legitimately need internal spaces
}

function validateKYCField(value: any, options: ValidationOptions): ValidationResult {
  // Handle null/undefined for optional fields
  if (!options.isRequired && (value === null || value === undefined || value === '')) {
    return { isValid: true };
  }
  
  // Convert to string and trim
  const stringValue = String(value);
  const trimmedValue = stringValue.trim();
  
  // Reject whitespace-only inputs
  if (trimmedValue.length === 0) {
    return { isValid: false, errorCode: 'WHITESPACE_ONLY_INPUT' };
  }
  
  // Apply length constraints
  if (trimmedValue.length < (options.minLength || 1) || 
      trimmedValue.length > (options.maxLength || 255)) {
    return { isValid: false, errorCode: 'INVALID_LENGTH' };
  }
  
  // Apply custom field-specific validation
  if (options.customValidator && !options.customValidator(trimmedValue)) {
    return { isValid: false, errorCode: 'INVALID_FORMAT' };
  }
  
  return { isValid: true, sanitizedValue: trimmedValue };
}

// Field-specific validators
const KYCValidators = {
  name: (value: string) => /^[\p{L}\p{M}\s\-'\.]+$/u.test(value),
  numericOnly: (value: string) => /^\d+$/.test(value),
  alphanumeric: (value: string) => /^[A-Za-z0-9\s\-]+$/.test(value),
  // Add more as needed
};
```

# Acceptance Criteria

## Functional Requirements
- [ ] All mandatory KYC fields reject inputs containing only whitespace characters
- [ ] Optional fields accept empty values OR valid non-whitespace content
- [ ] Input trimming applied consistently before validation
- [ ] Field-specific validation rules work correctly for each KYC field type
- [ ] Legitimate single-character inputs accepted where appropriate (e.g., middle initial "J")
- [ ] International characters supported in name fields
- [ ] Existing valid customer data remains unaffected

## Technical Requirements
- [ ] Centralized validation utility created and documented
- [ ] Error codes follow existing translation patterns
- [ ] Validation applied consistently across all KYC forms
- [ ] Performance impact minimal (< 50ms additional validation time)

## Testing Requirements
- [ ] Unit tests cover all edge cases:
  - Empty strings for optional fields
  - Whitespace-only inputs (spaces, tabs, newlines)
  - Minimum/maximum length boundaries
  - Special characters in names
  - International Unicode characters
  - Numeric field formatting
- [ ] Integration tests verify form submission behavior
- [ ] QA verification on all KYC forms

## Documentation Requirements
- [ ] Existing Validation rules documented in technical documentation
- [ ] New Validation rules documented in technical documentation
- [ ] Error message translations added to appropriate locale files
- [ ] Code comments explain validation logic and edge cases

# Definition of Done
- [ ] Code implemented following existing patterns in codebase
- [ ] All unit and integration tests passing
- [ ] Code review completed by senior developer
- [ ] QA verification on staging environment
- [ ] Documentation updated
- [ ] Ready for production deployment