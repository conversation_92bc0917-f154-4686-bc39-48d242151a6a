{"v5SafeApp": "V5 SAFE App", "welcome": "Welcome on this starter kit, beware of your clicks, the whole thing is experimental", "welcomeTitle": "Welcome", "none": "None", "mainMenu": {"home": "Get Starter", "topics": "Topics"}, "leftMenu": {"home": "Home", "leads": "Leads", "applications": "Applications", "events": "Events", "signout": "Sign Out"}, "searchUserDrawer": {"placeholder": "Search using user name", "usersSelected": "Users Selected", "displayName": "Display name", "email": "Email"}, "errors": {"500": {"title": "500", "description": "Sorry, something went wrong."}, "404": {"title": "404", "description": "Sorry, the page you visited does not exist."}, "403": {"title": "403", "description": "Sorry, you are not authorized to access this page."}, "sessionTimeout": {"title": "Inactivity Timeout", "minutes_one": "{{count}} minute", "minutes_other": "{{count}} minutes", "description": "To ensure security and protect your personal data, your session automatically times out after $t(common:errors.sessionTimeout.minutes, {\"count\": {{count}} }) of inactivity."}, "expired": {"title": "Sorry, the page was not found.", "description": "The requested URL has either expired or does not exist."}, "leadGenFormUnavailable": {"title": "{{eventName}} is no longer available", "description": "Please contact the administrator."}, "sdm": {"updateNotAllowed": "Application not amendable", "missingCompanyCode": "Company Code is missing", "missingBank": "Bank is missing", "missingVehicleCondition": "Vehicle Condition is missing", "missingFinanceAndInsurance": "Finance or Insurance is missing", "missingFinanceAndInsuranceProduct": "Finance or Insurance product not found", "bankNotFound": "Bank not found", "applicationNotFound": "Application not found", "applicationExisted": "found same unique id with {{payloadId}}", "vsaNumberNotMutable": "vsaNumber cannot be updated"}, "bmw": {"updateNotAllowed": "Application not amendable", "missingCompanyCode": "Company Code is missing", "missingBank": "Bank is missing", "missingVehicleCondition": "Vehicle Condition is missing", "bankNotFound": "Bank not found", "vehicleNotFound": "Vehicle not found", "applicationNotFound": "Application not found", "applicationExisted": "found same unique id with {{payloadId}}", "vsaNumberNotMutable": "vsaNumber cannot be updated"}, "porsche": {"notfound": {"title": "We don't know 404. We only know 911.", "subtitle": "The page you're trying to access is currently unavailable.", "listingButton": "Back"}}}, "formErrors": {"required": "This field is required.", "passwordLength": "Password should be 8 - 40 characters.", "invalidEmail": "In<PERSON>id Email Address", "invalidPhone": "Invalid Number", "invalidColor": "Invalid Colour", "invalidPasswordFormat": "Password should follow the requirements.", "invalidPasswordRepeat": "Password do not match.", "invalidIdentityNumber": "Invalid Identity Number", "finSGNotSupported": "For FIN holders please approach Sales Consultant for financing application", "invalidPostalCode": "Invalid Postal Code", "invalidUAEId": "Invalid Emirates ID", "invalidDateOfBirth": "Not eligible as minimum age is {{minAge}}.", "invalidDateOfBirthUAE": "Not eligible as minimum age is 25.", "maxLessThanMin": "Max should be larger than or equal to Min", "defaultNotInRange": "Default should be larger than <PERSON> and less than <PERSON>", "urlIdentifier": "URL Identifier failed", "urlValidate": "URL formats invalid", "minArrayInput": "Please assign at least 1 module this feature", "existedTimeRange": "Time range exists", "urlSlug": "URL Slug Invalid", "periodInvalid": "End Time should not be the same as or before Start Time", "timeStringInvalid": "Invalid duration format. Please use the format '2w 4d 6h 45m'.", "missingTimeValue": "Missing Start/End Time", "maxInputCount": "Maximum character is reached: {{max}}", "numberBetween": "Value should be between {{min}} and {{max}}", "validatePercentage": "Value should not be more than 100%", "numberMoreThan0": "Value should be more than 0", "differentFinderApplicationModuleMessage": "Finder Application Modules selected should have New and Preowned", "deleteConfirmationRequired": "Confirmation is required", "currentVehicleModelYear": "4 Digits number only, can only be current year + 1 or lower", "currentVehiclePurchaseYear": "4 Digits number only, can only be current year or lower", "duplicateScenario": "Scenario already been used.", "invalidScenario": "<PERSON><PERSON><PERSON>", "invalidAppointmentScenario": "Invalid appointment scenario. Must be only one appointment selected", "utmUrlLengthy": "URL exceeds maximum length of 2048 characters", "utmUrlInsecure": "Invalid URL format. URL must start with http:// or https://", "utmUrlInvalid": "Invalid URL", "utmUrlWhitespaces": "URL contains invalid spaces. Please remove any spaces", "utmUrlNotEncoded": "Invalid characters detected in URL. Please use URL-encoded values", "utmUrlMissingCampaignParameter": "Missing required parameter: utm_campaign", "utmUrlMissingMediumParameter": "Missing required parameter: utm_medium", "utmUrlMissingSourceParameter": "Missing required parameter: utm_source", "utmUrlDuplicate": "Duplicate UTM parameters detected", "utmUrlInvalidDomain": "URL domain and path does not match Lead C@Pture Form", "verifyPhoneRequired": "Mobile number verification required.", "maxDecimalPlaces": "Value should not have more than {{maxDecimalPlaces}} decimal places.", "whitespaceOnlyInput": "This field cannot contain only spaces", "invalidNameFormat": "Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed", "invalidAddressFormat": "Invalid address format. Only letters, numbers, spaces, and common punctuation are allowed", "invalidBusinessNameFormat": "Invalid business name format", "invalidAlphanumericFormat": "Invalid format. Only letters, numbers, spaces, and hyphens are allowed", "invalidFormat": "Invalid format", "minLength": "Minimum {{minLength}} characters required", "maxLength": "Maximum {{maxLength}} characters allowed", "invalidNumber": "Invalid number format", "numberRange": "Number must be between {{minValue}} and {{maxValue}}", "noWhitespace": "This field cannot be empty or whitespace only", "invalidDocumentFormat": "Invalid document format. Only letters, numbers, and common punctuation are allowed", "singleCharacterAllowed": "Single character entries are allowed where contextually appropriate", "invalidKYCName": "Invalid name format. Only letters, hyphens, apostrophes, and periods are allowed", "invalidKYCAddress": "Invalid address format. Only letters, numbers, spaces, and common punctuation are allowed", "invalidKYCBusinessName": "Invalid business name format", "invalidKYCAlphanumeric": "Invalid format. Only letters, numbers, spaces, and hyphens are allowed", "invalidKYCNumeric": "Invalid number format"}, "signIn": "Sign in", "signOut": "Logout", "goToPrivate": "Admin", "formats": {"date": "{{date, DD MMM YYYY}}", "dateFull": "{{date, DD MMMM YYYY}}", "dateTime": "{{date, DD MMM YYYY hh:mm A}}", "dateTimeWithOffset": "{{date}} (UTC{{offset}})", "offset": "(UTC{{offset}})", "dateTimeFormat": "DD MMM YYYY hh:mm A", "dateFullFormat": "DD MMM YYYY", "timePicker": "hh:mm A", "mobilityTime": "{{date, HH:mm}}", "mobilityTimePicker": "HH:mm", "datePicker": "DD MMM YYYY", "monthPicker": "MMM YYYY", "dateTimePicker": "$t(common:formats.datePicker) $t(common:formats.timePicker)", "dateTimePickerWithOffset": "$t(common:formats.datePicker) $t(common:formats.timePicker) Z", "fullYear": "YYYY", "month": "MM"}, "formattedVersion": "{{ formattedDate }} by {{author}}", "formattedWithoutTimezone": " {{formattedWithoutTimezoneDate}} by {{author}}", "createdByFormattedVersion": "{{ formattedDate }}", "createdByFormattedWithoutTimezone": " {{formattedWithoutTimezoneDate}}", "utcOffset": "({{utcOffset}})", "messages": {"sessionTimeout": "Session revoked or timed out", "successLogout": "Successfully logged out", "maintenanceWarning": "System will shut down on {{ day }} at {{ time }}", "sizeExceedsLimit": "File size should not exceed {{ limit }}MB.", "switchConfirm": "Do you really want to switch?"}, "upload": {"button": "Upload", "messages": {"sizeExceedsLimit": "File size should not exceed {{ limit }}MB.", "fileError": "Sorry, looks like there's some issue when uploading your file. Please try again or try to upload another file."}, "uploadText": "Click or drag file to this area to upload", "uploadHint": "Size {{ size }}. Max. {{ maxFiles }} files. File formats - {{ formats }}", "uploadHintUAEDrivingLicense": "Max. {{ maxFiles }} files. File formats - {{ formats }}", "fileExtensionHint": "*Only accept: {{ formats }}", "imageRequirementsForDesktop": "Recommended Image Requirements: <br /> • Max file size: 2 MB<br /> • Recommended aspect ratio: 16:9 (e.g., 1920 x 1080 px)<br /> • Recommended file formats: JPEG, PNG, MP4", "imageRequirementsForMobile": "Recommended Image Requirements: <br /> • Max file size: 2 MB<br /> • Recommended aspect ratio: 9:16 (e.g., 720 x 1280 px)<br /> • Recommended file formats: JPEG, PNG, MP4"}, "sessionRevokeNotice": {"title": "Session revoked", "content": "Your session has been revoked.", "okText": "OK"}, "sessionRevokedPage": {"title": "Session revoked", "description": "Your session has been revoked."}, "search": "Search", "searchPlaceHolder": "Search {{text}} here", "reset": "Reset", "ok": "OK", "copyright": "{{copyright}} | Version {{version}}", "copyrightVersion": "Version {{version}}", "legalNotices": "Legal notice", "privacyPolicy": "Privacy Policy", "systemNotice": "System Notice", "options": {"none": "None", "empty": "-", "yesNo": {"yes": "Yes", "no": "No"}, "onOff": {"on": "ON", "off": "OFF"}, "emailProvider": {"system": "Appvantage SMTP", "smtp": "Custom SMTP"}, "smsProvider": {"system": "Appvantage SMS", "twilio": "<PERSON><PERSON><PERSON>"}, "decimals": {"zero": "0", "one": "1", "two": "2"}, "calculationRounding": {"none": "None", "tens": "To the nearest 10", "hundreds": "To the nearest 100", "thousands": "To the nearest 1000"}, "bankIntegrationProvider": {"email": "Email", "hlf": "HLF V1", "hlfV2": "HLF V2", "uob": "UOB", "dbs": "DBS", "bmwFs": "BMW FS", "maybank": "Maybank", "enbd": "ENBD"}, "insurerIntegrationProvider": {"email": "Email", "eazy": "Eazy"}, "mfaSettingType": {"smsOtp": "SMS OTP"}, "transportProtocol": {"email": "Email", "sms": "SMS"}, "theme": {"default": "<PERSON><PERSON><PERSON>", "porsche": "Porsche v2", "volkswagen": "Volkswagen", "skoda": "Skoda", "porscheV3": "Porsche v3"}, "applicationStage": {"lead": "Lead", "reservation": "Reservation", "financing": "Financing", "appointment": "Test Drive", "visitAppointment": "Showroom Visit", "insurance": "Insurance", "mobility": "Mobility", "tradeIn": "Trade In"}, "passwordConfiguration": {"identityAndDateOfBirth": "Identity + Date of Birth", "dateOfBirth": "Date of Birth", "random": "Random", "off": "Off"}, "assetCondition": {"new": "New", "preOwned": "Pre-owned"}, "dateTimeUnit": {"days": "day", "hours": "hr"}, "configurator": {"bannerTextPosition": {"left": "Left", "centre": "Centre", "right": "Right"}, "package": {"price": "Price", "description": "Description"}, "optionKinds": {"combo": "Combo", "multiSelect": "Multi Select", "singleSelect": "Single Select"}, "optionsTypes": {"price": "Price", "description": "Description"}, "comboTypes": {"dropdown": "Dropdown", "textField": "Text Field"}}, "bodyTypes": {"coupe": "Coupe", "convertible": "Convertible", "hatchback": "Hatchback", "mpv": "MPV", "suv": "SUV", "sedan": "Sedan", "van": "<PERSON>", "wagon": "Wagon"}, "engineType": {"petrol": "Petrol", "diesel": "Diesel", "hybrid": "Hybrid", "electric": "Electric"}, "moduleTypes": {"consentsAndDeclarations": "Consents & Declarations", "simpleVehicleManagement": "Vehicle Management", "localCustomerManagement": "Customer Management", "basicSigningModule": "Sign with OTP", "namirialSigningModule": "eSign with Nam<PERSON><PERSON>", "standardApplicationModule": "Showroom Sales", "eventApplicationModule": "Lead C@Pture Forms", "adyenPaymentModule": "<PERSON><PERSON><PERSON>", "myinfoModule": "Myinfo", "configuratorModule": "Configurators", "promoCodeModule": "Promo Code", "sdmModule": "SDM", "whatsappLiveChatModule": "Whatsapp", "userlikeChatbotModule": "Userlike", "websiteModule": "Web Page Builder", "mobilityModule": "Mobility Rental", "bmwModule": "External system to V5 SAFE App", "labelsModule": "Vehicle Labels", "finderVehicleModule": "Porsche Finder Vehicles", "appointmentModule": "Appointment - Test Drive", "visitAppointmentModule": "Appointment - Showroom Visit", "bankModule": "Financing", "finderVehicleManagement": "Porsche Finder Vehicle", "myInfoModule": "Myinfo", "ctsModule": "Porsche CTS", "fiservPaymentModule": "Fiserv", "insuranceModule": "Insurance", "porschePaymentModule": "Porsche Payment", "payGatePaymentModule": "PayGate", "ttbPaymentModule": "TTB", "finderApplicationPublicModule": "<PERSON> Finder - Public", "finderApplicationPrivateModule": "Porsche Finder - Private", "maintenanceModule": "Maintenance Page", "porscheMasterDataModule": "Porsche MasterData", "audiFinancingModule": "Audi Financing", "autoplayModule": "Autoplay Integration", "audiCalculationModule": "Audi Calculation", "giftVoucherModule": "Gift Codes", "tradeInModule": "APV Trade In", "audiSalesForceModule": "Audi SalesForce", "capModule": "Porsche C@P", "porscheIdModule": "Porsche ID", "porscheRetainModule": "Porsche Retain", "docusign": "Docusign", "oIDC": "OIDC IdP", "launchPadModule": "Launchpad", "marketing": "Marketing Module", "salesOfferModule": "Sales Offer", "vehicleDataWithPorscheCodeIntegrationModule": "Porsche Vehicle Data Integration"}, "stockStatuses": {"available": "Available", "manuallyDeducted": "Manually Deducted", "reserved": "Reserved", "systemDeducted": "System Deducted"}, "drivingLicenseType": {"qualified": "Qualified", "notApplicable": "Not Applicable", "foreigner": "Foreigner", "uae": "UAE Resident", "notHolding": "No Driving License"}, "drivingLicenceValidityCodes": {"valid": "<PERSON><PERSON>", "invalid": "Invalid", "expired": "Expired"}, "titles": {"mr": "Mr", "ms": "Ms", "mx": "Mx"}, "salutations": {"dr": "Dr", "mr": "Mr", "mdm": "Mdm", "mrs": "Mrs", "ms": "Ms"}, "citizenship": {"singaporeCitizenOrPr": "Singapore Citizen/PR", "malaysian": "Malaysian", "others": "Other Nationality"}, "customerDataMasking": {"direction": {"none": "None", "front": "Front", "back": "Back"}, "counts": {"one": "1", "two": "2", "three": "3", "four": "4", "five": "5"}}, "webpageBlockType": {"column": "Image & Text Carousel", "custom": "Custom", "image": "Image/List", "textImage": "Text/Image", "textCarousel": "Text Carousel"}, "webpageBlockBackground": {"white": "White", "black": "Black"}, "webpageBlockPosition": {"left": "Left", "right": "Right"}, "transmissionKind": {"manual": "Manual", "auto": "Auto"}, "dayofWeek": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}, "finderVehicle": {"condition": {"new": "New", "preowned": "Preowned", "porscheApproved": "Porsche Approved"}, "functionality": {"oneTime": "One Time", "permanent": "Permanent"}, "status": {"available": "Available", "reserved": "Reserved", "submitted": "Submitted"}, "state": {"show": "Show", "hide": "<PERSON>de"}}, "ownerIdType": {"business": "Business (e.g. 51234567M)", "clubOrAssociationOrOrganization": "Club/Association/Organisation (e.g. T08PQ1234A)", "company": "Company (e.g. 198912345K)", "foreignCompany": "Foreign Company (e.g. T08FC1234A)", "foreignIdentificationNumber": "Foreign Identification Number (e.g. F/*********)", "foreignPassport": "Foreign Passport (e.g. 12345678)", "government": "Government (e.g. T08GA1234A)", "limitedLiabilityPartnership": "Limited Liability Partnership (e.g. T08LL1234A)", "limitedPartnership": "Limited Partnership (e.g. T08LP1234A)", "malaysiaNric": "Malaysia NRIC (e.g. 200312345678)", "professional": "Professional (e.g. T08PQ1234A)", "singaporeNric": "Singapore NRIC (e.g. *********)", "statutoryBoard": "Statutory Board (e.g. T08GB1234A)"}, "coeCategory": {"catA": "Cat A", "catB": "Cat B", "catC": "Cat C", "catE": "Cat E"}, "gender": {"female": "Female", "male": "Male", "unknown": "Unknown", "notSpecified": "Not Specify"}, "maritalStatus": {"single": "Single", "married": "Married", "widowed": "Widowed", "divorced": "Divorced"}, "residentialStatus": {"alien": "Alien", "citizen": "Citizen", "pr": "PR", "unknown": "Unknown", "notApplicable": "Not Applicable", "selfOwned": "Self-owned", "relativeOwned": "Relative-owned", "rental": "Rental", "dormitory": "Dormitory", "others": "Others"}, "jobTitle": {"staff": "Staff", "supervisor": "Supervisor", "assistantManager": "Assistant Manager", "manager": "Manager", "director": "Director", "vicePresident": "Vice President", "president": "President", "others": "Others"}, "jobTitleTh": {"employee": "Employee", "companyOwner": "Company owner", "managingDirector": "Managing director", "chiefExecutive": "Chief executive", "freelanceSelfEmployed": "Freelance/self-employed", "retiredHousewifeStudent": "Retired; housewife/househusband; student", "others": "Others"}, "jobTitleJp": {"jpLawyer": "Lawyer", "jpOfficeWorker": "Office worker", "jpOrganizationPersonnel": "Organization personnel", "jpBoardDirector": "Member of the Board of Directors", "jpOrganizationOfficer": "Organization Officer", "jpSelfEmployee": "Self Employee", "jpDoctor": "Doctor", "jpDentist": "Dentist", "jpNationalCivilServant": "National civil servant", "jpLocalCivilServant": "Local civil servant", "jpCertifiedAccountant": "Certified public accountant", "jpLicensedTaxAccountant": "Licensed tax accountant", "jpTeacher": "Teacher", "jpFreelance": "Freelance", "jpPartTimeJob": "Part-time job", "jpArtist": "Artist", "jpAgricultureForestryFishing": "Agriculture, Forestry, Fishing", "jpHousewife": "Housewife", "jpStudent": "Student", "jpInoccupationHouseHelper": "Inoccupation, House helper", "others": "Others"}, "jobTitleKo": {"koEmployee": "Employee", "koBusinessOwner": "Business Owner", "koProfession": "Profession", "koCeo": "CEO", "koFreelancer": "Freelancer", "others": "Others"}, "employmentStatus": {"betweenJobs": "Between Jobs", "contract": "Contract", "fullTime": "Full Time", "partTime": "Part Time", "selfEmployed": "Self-Employed", "unemployed": "Unemployed"}, "relationshipApplicant": {"myself": "Myself", "spouse": "Spouse", "children": "Children", "parent": "Parent", "silbing": "<PERSON><PERSON><PERSON>", "colleague": "Colleague", "friend": "Friend", "relative": "Relative", "shareholder": "Shareholder", "corporatePrincipal": "Corporate Principal", "others": "Others"}, "education": {"primary": "Primary", "preparatory": "Preparatory", "secondary": "Secondary", "technicalSecondarySchool": "Technical Secondary School", "bachelor": "Bachelor", "master": "Master", "doctorate": "Doctorate"}, "addressType": {"residential": "Residential", "office": "Office"}, "incomeType": {"salaried": "Salaried", "selfEmployed": "Self-Employed"}, "residenceType": {"owned": "Owned", "rented": "Rented"}, "emirate": {"abuDhabi": "Abu Dhabi", "ajman": "<PERSON><PERSON><PERSON>", "dubai": "Dubai", "fujairah": "<PERSON><PERSON><PERSON><PERSON>", "rasAlKhaimah": "<PERSON><PERSON>", "sharjah": "Sharjah"}, "referenceDetailRelationship": {"friend": "Friend", "relative": "Relative"}, "downPaymentTo": {"branch": "Branch", "dealer": "Dealer"}, "vatInclusion": {"include": "Inc. VAT", "exclude": "Exc. VAT"}, "industryCategory": {}, "access": {"login": "<PERSON><PERSON>", "public": "Public"}, "active": {"active": "Active", "inactive": "Inactive"}, "enable": {"enabled": "Enabled", "disabled": "Disabled"}, "energyUnit": {"liters": "L/100km", "kwh": "kWh/100km"}, "autoplayApiVersion": {"v3": "V3.0"}, "finderVehicleCondition": {"new": "New", "preowned": "Preowned"}, "purchaseIntention": {"withinAMonth": "Within a month", "inOneToThreeMonths": "In one to three months", "inThreeToSixMonths": "In three to six months", "inMoreThanSixMonths": "In more than six months"}, "insuranceProductType": {"eazy": "Eazy", "ergoLookupTable": "Ergo <PERSON>up Table"}, "currentVehicleSource": {"soldByDealer": "Sold by Dealer", "vehicleInHouseHold": "Vehicle in Household", "previousOwnedVehicle": "Previous owned Vehicle", "purchaseAlternative": "Purchase Alternative", "tradeInByDealer": "Trade-in by Dealer"}, "currentVehicleOwnership": {"yes": "Owned", "no": "Not owned"}, "currentVehicleEquipmentLine": {"other": "Other", "basic": "Basic", "fullyEquipped": "Fully Equipped", "premium": "Premium", "sport": "Sport"}, "leadStage": {"Lead": "Lead", "Contact": "Contact", "LeadAndContact": "Lead and Contact"}, "layout": {"basicPro": "Basic Pro", "porscheV3": "Porsche V3"}, "purposeOfVisit": {"newVehicleEnquiryPurchase": "New Vehicle Enquiry/Purchase", "preOwnedVehicleEnquiryPurchase": "Pre-Owned Vehicle Enquiry/Purchase", "vehicleConfigurationConsultation": "Vehicle Configuration Consultation", "interestInTestDrive": "Interest in Test Drive", "vehicleCollectionDelivery": "Vehicle Collection/Delivery", "brandExperienceEventAttendance": "Brand Experience Event Attendance", "accessoriesMerchandiseShopping": "Accessories & Merchandise Shopping", "tradeInEvaluation": "Trade-In Evaluation"}, "intentType": {"scheduledAppointment": "Scheduled Appointment", "walkIn": "Walk-In"}, "salesOfferAgreement": {"specification": "Specification", "coe": "COE", "vsa": "VSA"}}, "author": {"system": "System", "customer": {"withName": "{{name}} (Customer)", "withoutName": "Customer"}, "salesforce": {"general": "{{identifier}} SF", "sdm": "SDM", "audi": "Audi"}}, "fields": {"updated": {"label": "Last Modified (UTC{{offset}})"}, "created": {"label": "Created"}, "socialMedia": {"imageAltText": {"label": "Image Alt Text"}, "url": {"label": "URL"}, "icon": {"label": "Icon", "tooltip": "Recommended 200x200 pixels in PNG/JPG/GIF.", "fileError": "Unable to upload as image format is not supported."}}}, "actions": {"back": "Back", "filter": "Filter", "sort": "Sort", "search": "Search", "cancel": "Cancel", "save": "Save", "action": "Action", "startAgain": "Start Again", "apply": "Apply", "bookNow": "Book Now"}, "environment": {"test": "Test", "live": "Live", "preProduction": "PreProduction", "production": "Production"}, "loading": {"content": "Loading..."}, "dealerSelector": {"empty": "Please select at least 1 dealer from top navigation bar"}, "pleaseSelect": "Please Select", "markDownInfo": "*Italic*<br />**Bold**<br /># Heading 1<br />## Heading 2<br />* Bulleted List<br />1. Numbered List<br />[Link](http://a.com)", "sgMarkDownInfo": "{{coeprice}} - COE Value<br />", "nzMarkDownInfo": "{{ppsr}} - PPSR Fees<br />{{establishment}} - Establishment Fees<br />{{nzfees}} - Sum of PPSR and Est Fees<br />", "rentalRequirement": "Text will be displayed on KYC Side Panel", "rentalDisclaimer": "Text will be displayed on Vehicle Details", "finderButton": "This controls both the Reserve Now and Apply for Financing buttons.", "homeDelivery": "Home Delivery", "homeDeliveryOption": "Home Delivery {{moduleDisplayName}}", "testDriveProcess": "This will enable tracking of Test Drive vehicle.", "pickUpLocation": "{{locationPickUpName}} {{moduleDisplayName}}", "deliveryAddress": "Delivery is available to your location within a radius of 25km from Porsche Centre Dubai. Subject to confirmation from the Porsche Drive Team.", "noHyphenAndSpace": "Please input without hyphen/spaces", "dataPurgeAfter": "Data will be deleted up to 1 month before the purging date.", "link": "Link", "characterCount": "{{count}}/{{maxLength}} characters", "promoCode": {"offValue": "{{value}}", "offValueWithPercentage": "{{value}} ({{percentage}}%)"}, "application": {"session": {"expiring": {"title": "Session is Expiring", "body": "Due to inactivity, your session will expire in {{seconds}} seconds."}, "expired": {"title": "Session has Expired", "body": "Due to inactivity, your session has expired. Your selected vehicle is no longer reserved. Please restart your selection."}, "actions": {"endNow": "End Now", "extendSession": "Extend Session", "ok": "OK"}}}, "user": {"session": {"expiring": {"title": "Session is Expiring", "body": "Due to inactivity, your session will expire in {{seconds}} seconds."}, "actions": {"endNow": "End Now", "extendSession": "Extend Session"}}}, "period": "{{from}} - {{to}}", "periodPlaceholder": {"start": "Start date", "end": "End date"}, "download": "Download", "selectAll": "Select All", "deselectAll": "Deselect All", "modal": {"deletionConfirmation": "Deletion Confirmation", "staticDeleteConfirmationText": "Are you sure want to delete this?", "dynamicDeleteConfirmationText": "Are you sure want to delete <strong>{{deletedItem}}</strong>?", "yes": "Yes", "cancel": "Cancel", "documentDeletionSuccess": "Document is successfully deleted.", "dynamicDeletionSuccess": "{{deletedItem}} is successfully deleted.", "documentDeletionFail": "Sorry, there's some problem while removing the document, please try again.", "dynamicDeletionFail": "Sorry, there's some problem while removing {{deletedItem}}, please try again."}, "date": {"day_one": "day", "day_other": "days"}, "default": "<PERSON><PERSON><PERSON>", "rangeText": "to", "transfer": {"assigned": "Assigned", "unassigned": "Unassigned"}, "moduleInput": {"flexibleDiscount": {"isEnabled": {"label": "Discount Enabled"}, "amountUnit": {"label": "Discount Amount Unit"}}}, "rewriteWithAI": {"label": "Rewrite with AI", "maxWordLimit": "The content exceeds the [{{maxWordLimit}}] word count and has not been processed by Rewrite with AI.", "success": "Content rewritten successfully"}, "mobileNoPlaceholder": "Enter phone number", "noResults": "No results found"}